import type { ReferenceDocument } from '@components/utilities';
import { DEFAULT_PAGE, DEFAULT_PAGE_SIZE } from '@cosmos/components/datatable';
import { auditHubControllerGetAuditCustomerRequestEvidencesOptions } from '@globals/api-sdk/queries';
import type { AuditHubEvidenceResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

/**
 * Controller for document search and download operations.
 * This controller creates its own ObservedQuery instances to avoid
 * interfering with shared state used by other views.
 */
export class DocumentSearchController {
    /**
     * Private query instance that won't affect shared state.
     */
    #documentSearchQuery = new ObservedQuery(
        auditHubControllerGetAuditCustomerRequestEvidencesOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    get isLoading(): boolean {
        return this.#documentSearchQuery.isLoading;
    }

    get searchResults(): AuditHubEvidenceResponseDto[] {
        return this.#documentSearchQuery.data?.data ?? [];
    }

    searchDocuments = (
        auditId: string,
        customerRequestId: number,
        documentName: string,
    ): void => {
        this.#documentSearchQuery.load({
            query: {
                page: DEFAULT_PAGE,
                limit: DEFAULT_PAGE_SIZE,
                q: documentName,
            },
            path: {
                auditId,
                customerRequestId,
            },
        });
    };

    findMatchingDocument = (
        refDoc: ReferenceDocument,
    ): AuditHubEvidenceResponseDto | null => {
        return (
            this.searchResults.find(
                (doc) =>
                    doc.name === refDoc.documentName &&
                    doc.type === refDoc.documentType,
            ) ?? null
        );
    };
}
