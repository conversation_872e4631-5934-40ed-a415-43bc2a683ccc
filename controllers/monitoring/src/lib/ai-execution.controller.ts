import { isEmpty } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import type { SocketEvent } from '@drata/enums';
import {
    aiExecutionGroupControllerGenerateExecutionMutation,
    aiExecutionGroupControllerGetAiExecutionGroupsOptions,
} from '@globals/api-sdk/queries';
import type { AiExecutionGroupResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    reaction,
    runInAction,
} from '@globals/mobx';
import { type ChannelTypes, sharedSocketController } from '@globals/socket';
import type { SummaryStatus } from './constants/monitoring-summaries.constants';
import type {
    AIExecutionControllerState,
    AIExecutionErrorParams,
    AIExecutionParams,
    AIExecutionSocketConfig,
    AIExecutionSocketData,
} from './types/monitoring-summaries.types';

/**
 * AI Execution Controller.
 *
 * Converts the useAIExecution hook functionality into a proper MobX controller.
 * Manages AI execution state, socket notifications, and provides reactive data access.
 */
class AIExecutionController implements AIExecutionControllerState {
    isInitialized = false;
    summaryStatus: SummaryStatus | null = null;
    pendingExecutions: number | null = null;
    currentParams: AIExecutionParams | null = null;
    socketConfig: AIExecutionSocketConfig | null = null;
    errorParams: AIExecutionErrorParams | null = null;

    constructor() {
        makeAutoObservable(this);
        this.init();
    }

    /**
     * Queries and Mutations.
     */
    executionGroupsQuery = new ObservedQuery(
        aiExecutionGroupControllerGetAiExecutionGroupsOptions,
    );

    generateExecutionMutation = new ObservedMutation(
        aiExecutionGroupControllerGenerateExecutionMutation,
        {
            onSuccess: () => {
                runInAction(() => {
                    this.summaryStatus = 'LOADING';
                });
                // Invalidate to refresh data
                this.executionGroupsQuery.invalidate();
            },
            onError: () => {
                runInAction(() => {
                    this.summaryStatus = 'ERROR';
                });
                this.showErrorSnackbar();
            },
        },
    );

    /**
     * Initialization.
     */
    init(): void {
        if (this.isInitialized) {
            return;
        }

        reaction(
            () => sharedSocketController.isInitialized,
            (ready) => {
                if (!ready || this.isInitialized) {
                    return;
                }

                runInAction(() => {
                    this.isInitialized = true;
                });
            },
            { fireImmediately: true },
        );
    }

    /**
     * Socket Management.
     */
    subscribeToSocket(socketConfig: AIExecutionSocketConfig): void {
        this.socketConfig = socketConfig;

        if (!sharedSocketController.isInitialized) {
            // Set up a reaction to subscribe when socket becomes available
            reaction(
                () => sharedSocketController.isInitialized,
                (isInitialized) => {
                    if (!isInitialized || !this.socketConfig) {
                        return;
                    }

                    this.performSocketSubscription(this.socketConfig);
                },
                { fireImmediately: false },
            );

            return;
        }

        this.performSocketSubscription(socketConfig);
    }

    private performSocketSubscription(
        socketConfig: AIExecutionSocketConfig,
    ): void {
        sharedSocketController.subscribe({
            channelType: socketConfig.channelType as ChannelTypes,
            eventName: socketConfig.eventName as SocketEvent,
            callback: this.handleSocketNotification,
        });
    }

    handleSocketNotification = (data: AIExecutionSocketData): void => {
        if (!this.currentParams || !this.socketConfig) {
            return;
        }

        const { status, data: socketData } = data;
        const { featureId, processType, processFeature } = socketData;
        const {
            featureIds,
            processType: currentProcessType,
            processFeature: currentProcessFeature,
        } = this.currentParams;

        // Only handle events that match our current parameters
        if (
            !featureIds.includes(featureId) ||
            processType !== currentProcessType ||
            processFeature !== currentProcessFeature
        ) {
            return;
        }

        runInAction(() => {
            if (status === 'INPROGRESS') {
                this.pendingExecutions = (this.pendingExecutions ?? 0) + 1;
                this.summaryStatus = 'LOADING';
            } else if (status === 'COMPLETED' || status === 'ERROR') {
                this.pendingExecutions = Math.max(
                    (this.pendingExecutions ?? 1) - 1,
                    0,
                );

                if (this.pendingExecutions === 0) {
                    if (status === 'COMPLETED') {
                        // Refresh execution groups data
                        this.executionGroupsQuery.invalidate();
                        // Clear the manually set status so it computes from data
                        this.summaryStatus = null;
                    } else {
                        this.summaryStatus = 'ERROR';
                        this.showErrorSnackbar();
                    }
                }
            }
        });
    };

    /**
     * Public Actions.
     */
    loadExecutionGroups(
        params: AIExecutionParams,
        socketConfig: AIExecutionSocketConfig,
        errorParams: AIExecutionErrorParams,
    ): void {
        runInAction(() => {
            this.currentParams = params;
            this.errorParams = errorParams;
            this.summaryStatus = null;
            this.pendingExecutions = null;
        });

        // Subscribe to socket notifications
        this.subscribeToSocket(socketConfig);

        this.executionGroupsQuery.load({
            query: {
                processType: params.processType,
                processFeature: params.processFeature,
                page: params.page ?? 1,
                limit: params.limit ?? 20,
                'featureIds[]': params.featureIds,
                ...(params.executionStatuses && {
                    executionStatuses: params.executionStatuses,
                }),
            },
        });
    }

    generateExecution(data: {
        processType:
            | 'SUMMARY'
            | 'QUESTIONNAIRE'
            | 'OPEN_KNOWLEDGE'
            | 'FEEDBACK';
        processFeature:
            | 'QUESTIONNAIRE_RESPONSE'
            | 'MONITOR_TEST_LOGIC'
            | 'EVENT_TEST_FAILURE'
            | 'EXTRACT_SECURITY_QUESTIONS'
            | 'ANSWERING_QUESTION'
            | 'ANSWER_SECURITY_QUESTION'
            | 'MONITOR_TEST_INSTRUCTIONS'
            | 'MONITOR_TEST_TEMPLATE_INSTRUCTIONS';
        featureId: string;
    }): void {
        this.generateExecutionMutation.mutate({
            body: data,
        });
    }

    resetState(): void {
        runInAction(() => {
            this.summaryStatus = null;
            this.pendingExecutions = null;
            this.currentParams = null;
            this.socketConfig = null;
            this.errorParams = null;
        });
    }

    /**
     * Helper Methods.
     */
    private showErrorSnackbar(): void {
        const message =
            this.errorParams?.message ??
            t`Unable to process AI execution at this time.`;

        snackbarController.addSnackbar({
            id: 'ai-execution-error',
            props: {
                title: message,
                severity: 'critical',
                closeButtonAriaLabel: t`Close`,
            },
        });
    }

    private computeSummaryStatusFromExecutions(): SummaryStatus | null {
        const { executionGroups } = this;

        if (isEmpty(executionGroups)) {
            return null;
        }

        const globalStatuses = [
            ...new Set(
                executionGroups
                    .flatMap(({ executions }) => executions)
                    .map(({ status }) => status),
            ),
        ];

        if (isEmpty(globalStatuses)) {
            return null;
        }

        if (
            globalStatuses.includes('PENDING') ||
            globalStatuses.includes('INPROGRESS')
        ) {
            return 'LOADING';
        }

        if (globalStatuses.includes('ERROR')) {
            const isLlmError = executionGroups.some((executionGroup) =>
                executionGroup.executions.some((execution) =>
                    execution.errors.some((error) => error.code === 'LLM_FAIL'),
                ),
            );

            if (isLlmError) {
                return 'LLM_ERROR';
            }

            return 'ERROR';
        }

        return 'COMPLETED';
    }

    /**
     * Computed Properties.
     */
    get executionGroups(): AiExecutionGroupResponseDto[] {
        return this.executionGroupsQuery.data?.data ?? [];
    }

    get computedSummaryStatus(): SummaryStatus | null {
        // If we have a manually set status (from generation or socket), use that
        if (this.summaryStatus) {
            return this.summaryStatus;
        }

        // Otherwise, compute from existing execution groups
        return this.computeSummaryStatusFromExecutions();
    }

    get isLoading(): boolean {
        return this.executionGroupsQuery.isLoading;
    }

    get isLoadedOnce(): boolean {
        return Boolean(this.executionGroupsQuery.data);
    }

    get isProcessing(): boolean {
        return (
            this.generateExecutionMutation.isPending ||
            this.computedSummaryStatus === 'LOADING'
        );
    }

    get hasError(): boolean {
        return (
            this.computedSummaryStatus === 'ERROR' ||
            this.computedSummaryStatus === 'LLM_ERROR' ||
            Boolean(this.executionGroupsQuery.error)
        );
    }

    get data() {
        return {
            data: this.executionGroups,
            total: this.executionGroups.length,
        };
    }

    /**
     * Refresh execution groups data
     * Simplified version since ObservedQuery handles the complexity.
     */
    refreshExecutionGroups(): void {
        if (!this.currentParams) {
            return;
        }

        this.executionGroupsQuery.load({
            query: {
                processType: this.currentParams.processType,
                processFeature: this.currentParams.processFeature,
                'featureIds[]': this.currentParams.featureIds,
                page: this.currentParams.page ?? 1,
                limit: this.currentParams.limit ?? 20,
                ...(this.currentParams.executionStatuses && {
                    executionStatuses: this.currentParams.executionStatuses,
                }),
            },
        });
    }

    /**
     * Helper method to get in-progress executions count.
     */
    get inProgressExecutionsCount(): number {
        return this.executionGroups.filter((executionGroup) =>
            executionGroup.executions.some(
                (execution) => execution.status === 'INPROGRESS',
            ),
        ).length;
    }

    /**
     * Helper method to get all execution data.
     */
    get allExecutionData() {
        return this.executionGroups.flatMap((summary) =>
            summary.executions.map((execution) => execution.data),
        );
    }

    /**
     * Check if processing is complete for all features.
     */
    get isProcessingComplete(): boolean {
        if (!this.currentParams) {
            return false;
        }

        const nonErrorExecutionGroups = this.executionGroups.filter(
            (group) =>
                !group.executions.some(
                    (execution) => execution.status === 'ERROR',
                ),
        );

        const numberOfEvents = this.currentParams.featureIds.length;
        const isProcessing =
            this.pendingExecutions !== 0 &&
            numberOfEvents - nonErrorExecutionGroups.length ===
                this.pendingExecutions;

        return !isProcessing && this.pendingExecutions === 0;
    }
}

export const sharedAIExecutionController = new AIExecutionController();
