import { isEmpty } from 'lodash-es';
import { t } from '@globals/i18n/macro';

/**
 * Execution status types for monitoring summaries
 * Based on the original useAIExecution patterns but adapted for multiverse.
 */
export type ExecutionStatus = 'ERROR' | 'COMPLETED' | 'INPROGRESS' | 'PENDING';

/**
 * Summary status types for tracking overall generation state.
 */
export type SummaryStatus = 'LOADING' | 'ERROR' | 'COMPLETED' | 'LLM_ERROR';

/**
 * Error codes for execution failures.
 */
export type ExecutionError = 'LLM_FAIL';

/**
 * Process types for AI execution.
 */
export type ProcessType = 'SUMMARY';

/**
 * Process features for different types of summaries.
 */
export type ProcessFeature = 'EVENT_TEST_FAILURE';

/**
 * User-friendly error messages for different failure scenarios.
 */
export const getErrorMessage = (status: SummaryStatus): string => {
    switch (status) {
        case 'ERROR': {
            return t`Unable to generate summary. Please try again.`;
        }
        case 'LLM_ERROR': {
            return t`AI could not generate a response. Please try again.`;
        }
        default: {
            return t`An unexpected error occurred. Please try again.`;
        }
    }
};

/**
 * User-friendly status messages for different states.
 */
export const getStatusMessage = (status: SummaryStatus): string => {
    switch (status) {
        case 'LOADING': {
            return t`Generating summary...`;
        }
        case 'COMPLETED': {
            return t`Summary ready for download`;
        }
        case 'ERROR': {
            return t`Generation failed`;
        }
        case 'LLM_ERROR': {
            return t`AI generation failed`;
        }
        default: {
            return t`Unknown status`;
        }
    }
};

/**
 * Button labels for different states.
 */
export const getButtonLabel = (
    status: SummaryStatus | null,
    isGenerating: boolean,
): string => {
    if (isGenerating) {
        return t`Generating...`;
    }

    if (status === 'COMPLETED') {
        return t`Download CSV`;
    }

    return t`Generate Summary CSV`;
};

/**
 * Determine summary status from execution groups data
 * Based on the original getSummaryStatus helper but adapted for multiverse types.
 */
export const getSummaryStatusFromExecutionGroups = (
    executionGroups?: {
        executions: {
            status: ExecutionStatus;
            errors?: { code: string; message: string }[];
        }[];
    }[],
): SummaryStatus | null => {
    if (!executionGroups || isEmpty(executionGroups)) {
        return null;
    }

    const globalStatuses = [
        ...new Set(
            executionGroups
                .flatMap(({ executions }) => executions)
                .map(({ status }) => status),
        ),
    ];

    if (isEmpty(globalStatuses)) {
        return null;
    }

    if (
        globalStatuses.includes('PENDING') ||
        globalStatuses.includes('INPROGRESS')
    ) {
        return 'LOADING';
    }

    if (globalStatuses.includes('ERROR')) {
        const isLlmError = executionGroups.some((executionGroup) =>
            executionGroup.executions.some((execution) =>
                execution.errors?.some((error) => error.code === 'LLM_FAIL'),
            ),
        );

        if (isLlmError) {
            return 'LLM_ERROR';
        }

        return 'ERROR';
    }

    return 'COMPLETED';
};
