import { isNil } from 'lodash-es';
import { sharedEventsController } from '@controllers/events';
import { sharedWorkspaceMonitorsController } from '@controllers/workspace-monitors';
import { SocketEvent } from '@drata/enums';
import { monitorsControllerGenerateSummariesCsvMutation } from '@globals/api-sdk/queries';
import {
    makeAutoObservable,
    ObservedMutation,
    runInAction,
} from '@globals/mobx';
import { downloadBlob } from '@helpers/download-file';
import { sharedAIExecutionController } from './ai-execution.controller';
import {
    getButtonLabel,
    getStatusMessage,
} from './constants/monitoring-summaries.constants';
import type { MonitoringSummariesControllerState } from './types/monitoring-summaries.types';

class MonitoringSummariesController
    implements MonitoringSummariesControllerState
{
    isInitialized = false;
    isGenerating = false;
    currentTestId: number | null = null;

    /**
     * Use the AI execution controller for execution-related functionality.
     */
    aiExecutionController = sharedAIExecutionController;

    constructor() {
        makeAutoObservable(this);
        this.init();
    }

    /**
     * Mutations.
     */
    generateSummariesCsvMutation = new ObservedMutation(
        monitorsControllerGenerateSummariesCsvMutation,
        {
            onSuccess: (response) => {
                runInAction(() => {
                    this.isGenerating = false;
                });

                // Handle CSV download like web version
                if (response && this.currentTestId) {
                    const blob = new Blob([response as unknown as string], {
                        type: 'text/csv',
                    });

                    // Generate filename like web version using actual test name
                    const testName =
                        sharedWorkspaceMonitorsController
                            .workspaceMonitorTestOverview?.name ?? 'Test';
                    const filename = `${testName}-summary.csv`;

                    downloadBlob(blob, filename);
                }
            },
            onError: () => {
                runInAction(() => {
                    this.isGenerating = false;
                });
            },
        },
    );

    init(): void {
        if (this.isInitialized) {
            return;
        }

        // Simple initialization - AI execution controller handles socket management
        runInAction(() => {
            this.isInitialized = true;
        });
    }

    /**
     * Check for existing execution groups to determine if CSV has already been generated.
     */
    checkExistingExecutions = (testId: number, featureIds?: string[]): void => {
        runInAction(() => {
            this.currentTestId = testId;
        });

        // Delegate to AI execution controller
        const params = {
            processType: 'SUMMARY' as const,
            processFeature: 'EVENT_TEST_FAILURE' as const,
            featureIds: featureIds ?? [],
            page: 1,
            limit: 20,
        };

        const socketConfig = {
            channelType: 'company' as const,
            eventName: SocketEvent.TEST_FAILURES_SUMMARY,
            caller: 'MonitoringSummariesController',
        };

        const errorParams = {
            message: 'Unable to load execution groups',
        };

        this.aiExecutionController.loadExecutionGroups(
            params,
            socketConfig,
            errorParams,
        );
    };

    /**
     * Public Actions.
     */
    generateSummariesCsv = (testId: number): void => {
        runInAction(() => {
            this.currentTestId = testId;
            this.isGenerating = true;
        });

        this.generateSummariesCsvMutation.mutate({
            path: { testId },
        });
    };

    downloadCsv = (): void => {
        if (!this.currentTestId) {
            return;
        }

        // Generate and download CSV directly like web version
        this.generateSummariesCsvMutation.mutate({
            path: { testId: this.currentTestId },
        });
    };

    resetGenerationState = (): void => {
        runInAction(() => {
            this.isGenerating = false;
            this.currentTestId = null;
        });
    };

    /**
     * Computed Properties.
     */
    get canDownload(): boolean {
        return (
            this.aiExecutionController.computedSummaryStatus === 'COMPLETED' &&
            Boolean(this.currentTestId)
        );
    }

    get isProcessing(): boolean {
        return (
            this.isGenerating ||
            this.aiExecutionController.computedSummaryStatus === 'LOADING'
        );
    }

    get statusMessage(): string {
        const summaryStatus = this.aiExecutionController.computedSummaryStatus;

        if (summaryStatus) {
            return getStatusMessage(summaryStatus);
        }

        return '';
    }

    get buttonLabel(): string {
        return getButtonLabel(
            this.aiExecutionController.computedSummaryStatus,
            this.isGenerating,
        );
    }

    get hasError(): boolean {
        const summaryStatus = this.aiExecutionController.computedSummaryStatus;

        return summaryStatus === 'ERROR' || summaryStatus === 'LLM_ERROR';
    }

    /**
     * State getters for UI logic.
     */
    get isGenerateState(): boolean {
        const summaryStatus = this.aiExecutionController.computedSummaryStatus;
        const { executionGroups } = this.aiExecutionController;
        const executionGroupsData = {
            data: executionGroups,
            total: executionGroups.length,
        };
        const { failedFeatureIds } = sharedEventsController;

        return (
            isNil(summaryStatus) ||
            summaryStatus === 'ERROR' ||
            (!isNil(executionGroupsData) &&
                executionGroupsData.total < failedFeatureIds.length)
        );
    }

    get isLoadingState(): boolean {
        const summaryStatus = this.aiExecutionController.computedSummaryStatus;

        return !isNil(summaryStatus) && summaryStatus === 'LOADING';
    }

    get isErrorState(): boolean {
        const summaryStatus = this.aiExecutionController.computedSummaryStatus;

        return !isNil(summaryStatus) && summaryStatus === 'LLM_ERROR';
    }
}

export const sharedMonitoringSummariesController =
    new MonitoringSummariesController();
