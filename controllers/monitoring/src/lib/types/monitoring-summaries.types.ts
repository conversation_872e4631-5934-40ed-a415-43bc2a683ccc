import type {
    ExecutionStatus,
    SummaryStatus,
} from '../constants/monitoring-summaries.constants';

/**
 * Socket event data structure for test failures summary events
 * Based on the original useAIExecution patterns.
 */
export interface TestFailuresSummarySocketData {
    testId: number;
    status?: ExecutionStatus;
    featureId?: string;
    processType?: string;
    processFeature?: string;
    executionId?: string;
    error?: {
        code: string;
        message: string;
    };
}

/**
 * Execution data structure for tracking individual executions.
 */
export interface ExecutionData {
    id: string;
    status: ExecutionStatus;
    featureId: string;
    processType: string;
    processFeature: string;
    data?: unknown;
    errors?: {
        code: string;
        message: string;
    }[];
    createdAt: string;
    updatedAt: string;
}

/**
 * Execution group data structure.
 */
export interface ExecutionGroupData {
    id: string;
    featureId: string;
    processType: string;
    processFeature: string;
    executions: ExecutionData[];
    feedbacks: {
        id: string;
        type: string;
        reason?: string;
        comment?: string;
    }[];
    createdAt: string;
    updatedAt: string;
}

/**
 * CSV generation request parameters.
 */
export interface CsvGenerationParams {
    testId: number;
    processType?: string;
    processFeature?: string;
    featureIds?: string[];
}

/**
 * CSV generation response data.
 */
export interface CsvGenerationResponse {
    url?: string;
    signedUrl?: string;
    filename?: string;
    status: 'PENDING' | 'COMPLETED' | 'ERROR';
    message?: string;
}

/**
 * Controller state interface for type safety.
 */
export interface MonitoringSummariesControllerState {
    isInitialized: boolean;
    isGenerating: boolean;
    currentTestId: number | null;
}

/**
 * Socket subscription configuration.
 */
export interface SocketSubscriptionConfig {
    channelType: 'company' | 'user' | 'auditHub';
    eventName: string;
    callback: (data: TestFailuresSummarySocketData) => void;
}

/**
 * Execution tracking parameters for monitoring progress.
 */
export interface ExecutionTrackingParams {
    testId: number;
    featureIds: string[];
    processType: string;
    processFeature: string;
}

/**
 * AI Execution Controller specific types.
 */

/**
 * Parameters for AI execution requests.
 */
export interface AIExecutionParams {
    processType: 'SUMMARY' | 'QUESTIONNAIRE' | 'OPEN_KNOWLEDGE' | 'FEEDBACK';
    processFeature:
        | 'QUESTIONNAIRE_RESPONSE'
        | 'MONITOR_TEST_LOGIC'
        | 'EVENT_TEST_FAILURE'
        | 'EXTRACT_SECURITY_QUESTIONS'
        | 'ANSWERING_QUESTION'
        | 'ANSWER_SECURITY_QUESTION'
        | 'MONITOR_TEST_INSTRUCTIONS'
        | 'MONITOR_TEST_TEMPLATE_INSTRUCTIONS';
    featureIds: string[];
    page?: number;
    limit?: number;
    executionStatuses?: ExecutionStatus[];
}

/**
 * Socket configuration for AI execution events.
 */
export interface AIExecutionSocketConfig {
    channelType: 'company' | 'user' | 'auditHub';
    eventName: string;
    caller: unknown;
}

/**
 * Error configuration for AI execution.
 */
export interface AIExecutionErrorParams {
    message: string;
}

/**
 * Socket data structure for AI execution events.
 */
export interface AIExecutionSocketData {
    status: ExecutionStatus;
    data: {
        featureId: string;
        processType:
            | 'SUMMARY'
            | 'QUESTIONNAIRE'
            | 'OPEN_KNOWLEDGE'
            | 'FEEDBACK';
        processFeature:
            | 'QUESTIONNAIRE_RESPONSE'
            | 'MONITOR_TEST_LOGIC'
            | 'EVENT_TEST_FAILURE'
            | 'EXTRACT_SECURITY_QUESTIONS'
            | 'ANSWERING_QUESTION'
            | 'ANSWER_SECURITY_QUESTION'
            | 'MONITOR_TEST_INSTRUCTIONS'
            | 'MONITOR_TEST_TEMPLATE_INSTRUCTIONS';
    };
}

/**
 * AI Execution Controller state interface.
 */
export interface AIExecutionControllerState {
    isInitialized: boolean;
    summaryStatus: SummaryStatus | null;
    pendingExecutions: number | null;
    currentParams: AIExecutionParams | null;
    socketConfig: AIExecutionSocketConfig | null;
    errorParams: AIExecutionErrorParams | null;
}
