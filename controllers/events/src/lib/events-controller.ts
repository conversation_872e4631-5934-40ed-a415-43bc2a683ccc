import { isEmpty } from 'lodash-es';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import { eventsControllerListEventsOptions } from '@globals/api-sdk/queries';
import type { EventResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    COLUMN_NAMES_TO_SORT_IDS_MAP,
    DEFAULT_SORT_COLUMN,
    DEFAULT_SORT_ORDER,
} from './events-controller-constants';
import { formatEventsFilters } from './helpers/format-events-filters.helper';

type SortIds = (typeof COLUMN_NAMES_TO_SORT_IDS_MAP)[number];

class EventsController {
    eventQuery = new ObservedQuery(eventsControllerListEventsOptions);

    constructor() {
        makeAutoObservable(this);
    }

    get events(): EventResponseDto[] {
        return this.eventQuery.data?.data ?? [];
    }

    get total(): number {
        return this.eventQuery.data?.total ?? 0;
    }

    get isLoading(): boolean {
        return this.eventQuery.isLoading;
    }

    get failedFeatureIds(): string[] {
        return this.events
            .filter(
                ({ status, haveFailedResources }) =>
                    status === 'FAILED' && haveFailedResources,
            )
            .map(({ id }) => id);
    }

    loadEvents = (
        params?: FetchDataResponseParams & {
            additionalQuery?: Record<string, unknown>;
        },
    ): void => {
        const {
            pagination,
            sorting,
            globalFilter: { search, filters },
            additionalQuery,
        } = params ?? {
            pagination: {},
            sorting: [],
            globalFilter: { search: '', filters: {} },
            additionalQuery: {},
        };

        const { pageSize = 10, page = 1 } = pagination;

        const processedFilters = formatEventsFilters(filters);

        when(
            () => sharedWorkspacesController.isLoaded,
            () => {
                const query = {
                    page,
                    limit: pageSize,
                    sort: DEFAULT_SORT_COLUMN as SortIds,
                    workspaceId:
                        sharedWorkspacesController.currentWorkspace?.id,
                    sortDir: DEFAULT_SORT_ORDER as 'ASC' | 'DESC',
                    ...(search ? { q: search } : null),
                    ...(processedFilters?.category
                        ? {
                              category: processedFilters.category,
                          }
                        : null),
                    ...(processedFilters?.userId
                        ? {
                              userId: processedFilters.userId,
                          }
                        : null),
                    ...(processedFilters?.source
                        ? {
                              source: processedFilters.source as
                                  | 'APP'
                                  | 'AUTOPILOT'
                                  | 'PUBLIC_API'
                                  | 'VENDOR_QUESTIONNAIRE'
                                  | 'SCHEDULED'
                                  | 'WORKFLOW'
                                  | 'DRATA_POLICY'
                                  | undefined,
                          }
                        : null),
                    ...(processedFilters?.type
                        ? {
                              type: processedFilters.type,
                          }
                        : null),
                    // Add any additional query parameters
                    ...additionalQuery,
                };

                if (!isEmpty(sorting)) {
                    const sortId = sorting[0].id;

                    if (
                        COLUMN_NAMES_TO_SORT_IDS_MAP.includes(sortId as SortIds)
                    ) {
                        query.sort = sortId as SortIds;
                        query.sortDir = sorting[0].desc ? 'DESC' : 'ASC';
                    }
                    query.sortDir = sorting[0].desc ? 'DESC' : 'ASC';
                }

                this.eventQuery.load({
                    query,
                });
            },
        );
    };
}

export const sharedEventsController = new EventsController();
