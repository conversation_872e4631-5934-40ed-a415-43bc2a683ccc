import { isEmpty, isError, isObject } from 'lodash-es';
import {
    sharedAccessReviewController,
    sharedAccessReviewPeriodApplicationController,
    sharedAccessReviewPeriodApplicationSummaryController,
    sharedAccessReviewPeriodApplicationUsersController,
} from '@controllers/access-reviews';
import { snackbarController } from '@controllers/snackbar';
import type { CosmosFileObject } from '@cosmos/components/file-upload';
import { SocketEvent } from '@drata/enums';
import {
    accessReviewApplicationControllerCreateApplicationMutation,
    accessReviewApplicationControllerDeleteApplicationMutation,
    accessReviewApplicationControllerGetReviewApplicationOptions,
    accessReviewApplicationControllerUpdateAccessApplicationStatusMutation,
    accessReviewUserControllerCreateAccessReviewUsersBulkMutation,
    accessReviewUserControllerPostAccessReviewUsersCsvValidationMutation,
} from '@globals/api-sdk/queries';
import type {
    AccessReviewApplicationControllerUpdateAccessApplicationStatusData,
    AccessReviewApplicationResponseDto,
    AccessReviewUserControllerPostAccessReviewUsersCsvValidationData,
    AccessReviewUsersBulkRequestDto,
    AccessReviewUsersCsvValidationResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import {
    action,
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    runInAction,
    when,
} from '@globals/mobx';
import { providers } from '@globals/providers';
import { sharedSocketController } from '@globals/socket';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { getSourceLabel } from './access-review.constant';
import { activeAccessReviewsApplicationsUserController } from './access-reviews-applications-user-controller';
import {
    extractFileFromInput,
    lookForInvalidDataOnCSV,
} from './helpers/csv-validation.helper';

/**
 * Safely extracts status code from an error object.
 */
const getErrorStatusCode = (error: unknown): string | number => {
    if (isObject(error) && 'status' in error) {
        return error.status as string | number;
    }
    if (isObject(error) && 'statusCode' in error) {
        return error.statusCode as string | number;
    }

    return 'unknown';
};

interface CreateApplicationParams {
    name: string;
    websiteUrl: string;
    reviewersId: string[];
}

function getErrorTitle(error: Error): string {
    if (isEmpty(error)) {
        return t`Couldn't remove at the moment`;
    }
    if (isError(error)) {
        return error.message;
    }

    return t`An error occurred`;
}

class AccessReviewsApplicationsController {
    constructor() {
        makeAutoObservable(this);
    }

    deleteApplicationMutation = new ObservedMutation(
        accessReviewApplicationControllerDeleteApplicationMutation,
    );

    accessReviewsApplications = new ObservedQuery(
        accessReviewApplicationControllerGetReviewApplicationOptions,
    );
    /**
     * Make sure this property is accessed only in reactive contexts.
     */
    createApplicationMutation = new ObservedMutation(
        accessReviewApplicationControllerCreateApplicationMutation,
    );

    editAccessReviewUserCsvValidation = new ObservedMutation(
        accessReviewUserControllerPostAccessReviewUsersCsvValidationMutation,
    );

    createAccessReviewUsersBulkMutation = new ObservedMutation(
        accessReviewUserControllerCreateAccessReviewUsersBulkMutation,
    );

    completeAccessReviewApplicationStatus = new ObservedMutation(
        accessReviewApplicationControllerUpdateAccessApplicationStatusMutation,
    );

    get accessReviewsApplicationsDetails() {
        return (
            this.accessReviewsApplications.data ??
            ({} as AccessReviewApplicationResponseDto)
        );
    }

    get isAccessReviewsApplicationsLoading(): boolean {
        return this.accessReviewsApplications.isLoading;
    }

    get isManuallyAddedApplication(): boolean {
        return (
            this.accessReviewsApplicationsDetails.source === 'MANUALLY_ADDED'
        );
    }
    get warnings(): AccessReviewApplicationResponseDto['warnings'] {
        return this.accessReviewsApplicationsDetails.warnings;
    }

    get accessReviewsApplicationsProvider() {
        const { data } = this.accessReviewsApplications;

        const clientType = data?.clientType;
        const applicationName = data?.name;

        if (clientType && clientType in providers) {
            return providers[clientType as keyof typeof providers];
        }

        if (applicationName && applicationName in providers) {
            return providers[applicationName as keyof typeof providers];
        }

        return {
            name: applicationName ?? '',
            logo: data?.logo ?? providers.CUSTOM.logo,
            id: clientType,
        };
    }

    get accessReviewsApplicationsSource() {
        const source = this.accessReviewsApplications.data?.source;

        if (!source) {
            return '';
        }

        return getSourceLabel(source);
    }

    get isLoading(): boolean {
        return this.accessReviewsApplications.isLoading;
    }

    loadAccessReviewApplicationDetails = (id: number) => {
        if (isNaN(id)) {
            throw new TypeError('applicationId and clientType are required');
        }

        this.accessReviewsApplications.load({
            path: {
                id,
            },
        });
    };

    deleteApplication = (id: number, navigation?: (path: string) => void) => {
        openConfirmationModal({
            title: t`Remove application`,
            body: t`Are you sure you want to remove this application? You can add it again later.`,
            confirmText: t`Remove`,
            cancelText: t`Cancel`,
            type: 'danger',
            onConfirm: () => {
                this.deleteApplicationMutation
                    .mutateAsync({
                        path: { id },
                    })
                    .then(() => {
                        snackbarController.addSnackbar({
                            id: 'application-removed',
                            props: {
                                title: t`Application removed successfully`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });

                        if (navigation) {
                            navigation(
                                `/workspaces/${sharedWorkspacesController.currentWorkspace?.id}/governance/access-review/applications`,
                            );
                        } else {
                            sharedAccessReviewController.accessReview.invalidate();
                        }
                        closeConfirmationModal();
                    })
                    .catch((error) => {
                        logger.error({
                            message:
                                'Failed to delete access review application',
                            additionalInfo: {
                                applicationId: id,
                                action: 'deleteApplication',
                            },
                            errorObject: {
                                message: isError(error)
                                    ? error.message
                                    : String(error),
                                statusCode: getErrorStatusCode(error),
                            },
                        });

                        snackbarController.addSnackbar({
                            id: 'application-remove-error',
                            props: {
                                title: getErrorTitle(error as Error),
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                    });
            },
            onCancel: () => {
                closeConfirmationModal();
            },
        });
    };

    createApplicationMutationCall = (
        { name, websiteUrl, reviewersId }: CreateApplicationParams,
        navigate: (path: string) => void,
        onClose: () => void,
        onApplicationCreated?: (
            application: AccessReviewApplicationResponseDto,
        ) => void,
        onError?: () => void,
    ) => {
        if (isEmpty(name) || isEmpty(websiteUrl)) {
            throw new Error(t`Name and website Url are required`);
        }

        this.createApplicationMutation
            .mutateAsync({
                body: {
                    name,
                    websiteUrl,
                    reviewersIds: reviewersId.map(Number),
                },
            })
            .then(() => {
                snackbarController.addSnackbar({
                    id: 'application-create-success',
                    props: {
                        title: t`Application successfully added.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            })
            .catch((error: Error) => {
                logger.error({
                    message: 'Failed to create access review application',
                    additionalInfo: {
                        applicationName: name,
                        websiteUrl,
                        reviewersCount: reviewersId.length,
                        action: 'createApplication',
                    },
                    errorObject: {
                        message: error.message,
                        statusCode: getErrorStatusCode(error),
                    },
                });

                snackbarController.addSnackbar({
                    id: 'application-create-error',
                    props: {
                        title: t`Failed to Create application.`,
                        description: t`Application already exists`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                onError?.();
                throw new Error(error.message);
            });

        when(
            () =>
                !this.createApplicationMutation.isPending &&
                sharedWorkspacesController.currentWorkspace?.id !== undefined,
            () => {
                const { response } = this.createApplicationMutation;
                const workspaceId =
                    sharedWorkspacesController.currentWorkspace?.id;

                if (!response || !isObject(response) || !('id' in response)) {
                    throw new Error(
                        t`Failed to get application ID from response`,
                    );
                }
                const accessApplicationId = (response as { id: number }).id;

                if (onApplicationCreated) {
                    onApplicationCreated(response);
                    onClose();
                } else {
                    const clientType = response.clientType || '';

                    onClose();
                    navigate(
                        `workspaces/${workspaceId}/governance/access-review/applications/${accessApplicationId}/personnel/${clientType}`,
                    );
                }
            },
        );
    };

    createApplication = action(this.createApplicationMutationCall);

    validateCsvFile = (
        data: File | Blob | CosmosFileObject | null = null,
    ): Promise<
        | AccessReviewUserControllerPostAccessReviewUsersCsvValidationData
        | undefined
    > => {
        const file = extractFileFromInput(data);

        if (!file) {
            return Promise.reject(new Error(t`No file provided`));
        }

        if (file.size === 0) {
            return Promise.reject(new Error(t`File is empty`));
        }

        return this.validateFileContent(file);
    };

    private validateFileContent(
        file: File | Blob,
    ): Promise<
        | AccessReviewUserControllerPostAccessReviewUsersCsvValidationData
        | undefined
    > {
        return lookForInvalidDataOnCSV(file).then((result) => {
            const [hasInvalidData, invalidConstant] = result;

            if (hasInvalidData) {
                return Promise.reject(
                    new Error(
                        t`File contains potentially malicious content: ${invalidConstant}`,
                    ),
                );
            }

            const wrapData = { error: [], file };

            return this.editAccessReviewUserCsvValidation
                .mutateAsync({ body: wrapData })
                .then(() => {
                    const { response } = this.editAccessReviewUserCsvValidation;

                    if (!response) {
                        return undefined;
                    }

                    return response as unknown as AccessReviewUserControllerPostAccessReviewUsersCsvValidationData;
                })
                .catch((error) => {
                    logger.error({
                        message:
                            'Failed to validate CSV file for access review',
                        additionalInfo: {
                            fileSize: file.size,
                            fileType: file.type,
                            action: 'validateCsvFile',
                        },
                        errorObject: {
                            message: isError(error)
                                ? error.message
                                : String(error),
                            statusCode: getErrorStatusCode(error),
                        },
                    });

                    snackbarController.addSnackbar({
                        id: 'access-review-validate-csv-error',
                        props: {
                            title: t`Unable to process CSV file.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return undefined;
                });
        });
    }

    createAccessReviewUsersBulk = (
        applicationId: number,
        identities: AccessReviewUsersBulkRequestDto['identities'],
        uploadMode: 'CHANGES_ONLY' | 'OVERWRITE_ALL',
        periodId?: number,
    ) => {
        return this.createAccessReviewUsersBulkMutation
            .mutateAsync({
                body: {
                    applicationId,
                    identities,
                    isOverWriteMode: uploadMode === 'OVERWRITE_ALL',
                    reviewPeriodId: periodId ?? 0, // this value is optional TS is requiring it
                },
            })
            .then(() => {
                snackbarController.addSnackbar({
                    id: 'access-review-users-bulk-create-success',
                    props: {
                        title: t`Personnel uploaded successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                runInAction(() => {
                    if (periodId) {
                        sharedAccessReviewPeriodApplicationUsersController.accessReviewPeriodApplicationUsers.invalidate();
                    }
                    activeAccessReviewsApplicationsUserController.accessReviewsApplicationsUser.invalidate();
                });
            })
            .catch((error) => {
                logger.error({
                    message: 'Failed to create access review users in bulk',
                    additionalInfo: {
                        applicationId,
                        identitiesCount: identities.length,
                        uploadMode,
                        periodId,
                        action: 'createAccessReviewUsersBulk',
                    },
                    errorObject: {
                        message: isError(error) ? error.message : String(error),
                        statusCode: getErrorStatusCode(error),
                    },
                });

                snackbarController.addSnackbar({
                    id: 'access-review-users-bulk-create-error',
                    props: {
                        title: t`Unable to upload personnel, please try again later`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                throw error;
            });
    };

    createAccessReviewUsersBulkAction = action(
        this.createAccessReviewUsersBulk,
    );

    handleCsvFileValidation = (
        rawFile: File,
        onValidationStart: () => void,
        onValidationSuccess: (
            data: AccessReviewUsersCsvValidationResponseDto,
        ) => void,
        onValidationError: () => void,
    ): Promise<void> => {
        onValidationStart();

        return runInAction(() => {
            return this.validateCsvFile(rawFile)
                .then((response) => {
                    if (!response) {
                        onValidationError();

                        return;
                    }

                    onValidationSuccess(
                        response as unknown as AccessReviewUsersCsvValidationResponseDto,
                    );
                })
                .catch((error) => {
                    logger.error({
                        message:
                            'Failed to handle CSV file validation for access review',
                        additionalInfo: {
                            fileName: rawFile.name,
                            fileSize: rawFile.size,
                            fileType: rawFile.type,
                            action: 'handleCsvFileValidation',
                        },
                        errorObject: {
                            message: isError(error)
                                ? error.message
                                : String(error),
                            statusCode: getErrorStatusCode(error),
                        },
                    });

                    onValidationError();
                });
        });
    };

    handlePersonnelUploadComplete = async (
        fileRef: { current: CosmosFileObject[] },
        validationData: AccessReviewUsersCsvValidationResponseDto | undefined,
        uploadMode: 'CHANGES_ONLY' | 'OVERWRITE_ALL',
        isActivePeriod: boolean,
        applicationId: number,
        periodId?: number,
        onSuccess?: () => void,
    ): Promise<void> => {
        // Validate file exists
        if (isEmpty(fileRef.current)) {
            snackbarController.addSnackbar({
                id: 'access-review-no-file-error',
                props: {
                    title: t`Please upload a CSV file first.`,
                    severity: 'warning',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return Promise.reject(new Error(t`No file uploaded`));
        }

        // Validate validation data exists and is valid
        if (!validationData) {
            return Promise.reject(new Error(t`No validation data available`));
        }

        if (validationData.status !== 'VALID') {
            return Promise.reject(new Error(t`File validation failed`));
        }

        const { identities } = validationData;

        // Filter out null usernames and create bulk request
        const filteredIdentities = identities.filter(
            (identity: { username: string | null }) =>
                identity.username !== null,
        ) as AccessReviewUsersBulkRequestDto['identities'];

        await this.createAccessReviewUsersBulkAction(
            applicationId,
            filteredIdentities,
            uploadMode,
            isActivePeriod && periodId ? Number(periodId) : undefined,
        );

        // Execute success callback
        if (onSuccess) {
            onSuccess();
        }
    };

    /**
     * Completes a review application with the provided data.
     *
     * @param periodId - The ID of the review period.
     * @param reviewApplicationId - The ID of the review application.
     * @param renewalScheduleType - The renewal schedule type (e.g. "ONE_YEAR").
     * @param reviewStatus - The status of the review (e.g. "APPROVED", "REJECTED", "OUT_OF_SCOPE").
     * @param additionalEvidence - Optional array of file objects to attach as evidence.
     * @returns Promise that resolves when the application is completed successfully.
     */
    completeReviewApplication = (
        periodId: number,
        reviewApplicationId: number,
        renewalScheduleType: AccessReviewApplicationControllerUpdateAccessApplicationStatusData['body']['renewalScheduleType'],
        reviewStatus: AccessReviewApplicationControllerUpdateAccessApplicationStatusData['body']['reviewStatus'],
        additionalEvidence?: File[],
    ) => {
        const formData = new FormData();

        additionalEvidence?.forEach((file) => {
            formData.append('file', file);
        });

        const files = formData.getAll('file') as File[];

        this.completeAccessReviewApplicationStatus
            .mutateAsync({
                path: {
                    periodId,
                    reviewAppId: reviewApplicationId,
                },
                body: {
                    renewalScheduleType,
                    reviewStatus,
                    'files[]': files,
                },
            })
            .then(() => {
                runInAction(() => {
                    this.accessReviewsApplications.invalidate();
                    sharedAccessReviewPeriodApplicationController.accessReviewPeriodApplication.invalidate();
                });

                snackbarController.addSnackbar({
                    id: 'access-review-complete-application-success',
                    hasTimeout: true,
                    props: {
                        title: t`Application completed successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                when(
                    () => sharedSocketController.isInitialized,
                    () => {
                        runInAction(() => {
                            this.subscribeToChannel();
                        });
                    },
                );
            })
            .catch((error) => {
                logger.error({
                    message: 'Failed to complete access review application',
                    additionalInfo: {
                        periodId,
                        reviewApplicationId,
                        renewalScheduleType,
                        reviewStatus,
                        additionalEvidenceCount:
                            additionalEvidence?.length || 0,
                        action: 'completeReviewApplication',
                    },
                    errorObject: {
                        message: isError(error) ? error.message : String(error),
                        statusCode: getErrorStatusCode(error),
                    },
                });

                snackbarController.addSnackbar({
                    id: 'access-review-complete-application-error',
                    props: {
                        title: t`Unable to complete application, please try again later`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                throw error;
            });
    };

    subscribeToChannel(): void {
        sharedSocketController.subscribe({
            channelType: 'company',
            eventName:
                SocketEvent.REVIEW_APPLICATION_EVIDENCE_GENERATION_COMPLETED,
            callback: () => {
                sharedAccessReviewPeriodApplicationSummaryController.accessReviewPeriodApplicationSummary.invalidate();
            },
        });
    }
}

export const activeAccessReviewsApplicationsController =
    new AccessReviewsApplicationsController();
