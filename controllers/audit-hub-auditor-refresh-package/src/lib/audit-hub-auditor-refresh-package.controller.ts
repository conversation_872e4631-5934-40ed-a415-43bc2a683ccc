import {
    sharedAuditHubAuditController,
    sharedAuditHubController,
} from '@controllers/audit-hub';
import { sharedAuditEvidenceQueryController } from '@controllers/audit-hub-auditor-client-actions';
import { snackbarController } from '@controllers/snackbar';
import { auditorControllerRefreshControlEvidencePackageMutation } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import {
    makeAutoObservable,
    ObservedMutation,
    runInAction,
    when,
} from '@globals/mobx';

class AuditHubAuditorRefreshPackageController {
    auditStarted = false;
    isSocketPending = false;
    auditorRefreshControlEvidencePackageMutation = new ObservedMutation(
        auditorControllerRefreshControlEvidencePackageMutation,
    );

    constructor() {
        makeAutoObservable(this);
    }

    postRefreshEvidencePackage = (auditId: string, productId: number) => {
        // Mutation returns fast, but socket could take a while, so we wait for it and set a true for isSocketPending
        this.isSocketPending = true;
        this.auditorRefreshControlEvidencePackageMutation.mutate({
            path: { auditorFrameworkId: auditId, xProductId: productId },
        });

        when(
            () => !this.isAuditorRefreshControlEvidencePackageLoading,
            () => {
                if (
                    this.auditorRefreshControlEvidencePackageMutation.hasError
                ) {
                    logger.error(
                        'refresh-control-evidence-package-error - Refresh Control Evidence Package Error ',
                    );
                    snackbarController.addSnackbar({
                        id: 'refresh-control-evidence-package-error',
                        props: {
                            title: t`Refresh Control Evidence Package Error`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                // generateAllCompany - handle success case
                runInAction(() => {
                    // Invalidate companies data - let reactive components refetch when needed
                    sharedAuditHubAuditController.getAllCompanies.invalidate();
                });

                const isDownloadOnlyAudit =
                    sharedAuditHubController.auditByIdData?.framework
                        .auditType === 'DOWNLOAD_ONLY_AUDIT';

                // reset status to set proper value on evidence button label, generating evidence package takes a while to reflect on the ping status
                sharedAuditEvidenceQueryController.getLatestAuditorFrameworkEvidencePingQuery.invalidate();

                if (isDownloadOnlyAudit) {
                    snackbarController.addSnackbar({
                        id: 'refresh-control-evidence-package-success',
                        props: {
                            title: t`You will be notified via email when your download is ready.`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        id: 'refresh-control-evidence-package-success',
                        props: {
                            title: t`Auditors will be notified via email when your download is ready.`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }
            },
        );
    };

    get auditorRefreshControlEvidencePackageData() {
        return this.auditorRefreshControlEvidencePackageMutation.response;
    }

    get isAuditorRefreshControlEvidencePackageLoading(): boolean {
        return this.auditorRefreshControlEvidencePackageMutation.isPending;
    }
}

export const sharedAuditHubAuditorRefreshPackageController =
    new AuditHubAuditorRefreshPackageController();
