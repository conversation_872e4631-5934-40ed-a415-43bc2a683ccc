import { isEmpty, isNil } from 'lodash-es';
import { sharedLinkControlsController } from '@controllers/evidence-library';
import { snackbarController } from '@controllers/snackbar';
import type { VendorRiskUploadStatus } from '@controllers/vendors';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import {
    riskManagementControllerCreateCustomRiskMutation,
    riskManagementControllerUploadDocumentsMutation,
} from '@globals/api-sdk/queries';
import type {
    AuditFrameworkControlResponseDto,
    RiskRequestDto,
    RiskResponseDto,
} from '@globals/api-sdk/types';
import { zRiskRequestDto } from '@globals/api-sdk/zod';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    toJS,
    when,
} from '@globals/mobx';
import { formatDate } from '@helpers/date-time';
import type { FormValues } from '@ui/forms';
import type { CreateRiskCustomMutationType } from '../types/create-risk-custom-mutation.type';
import {
    getAdaptedControls,
    getInherentValue,
    getResidualScore,
    getResidualValue,
    getScore,
    getUniqueIdsList,
} from './helpers/risks-adapter.helper';

class CreateRiskMutationController {
    risk: RiskResponseDto | null = null;
    uploadStatus: VendorRiskUploadStatus = 'NOT_STARTED';
    mutatedRiskDetails: RiskRequestDto | null = null;
    mappedControls: ListBoxItemData[] = [];
    createRiskWizardData: CreateRiskCustomMutationType | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    createRiskMutation = new ObservedMutation(
        riskManagementControllerCreateCustomRiskMutation,
    );

    vendorRiskUploadDocumentMutation = new ObservedMutation(
        riskManagementControllerUploadDocumentsMutation,
    );

    setCreateRiskWizardDataOverride = (data: FormValues) => {
        this.createRiskWizardData = {
            ...this.createRiskWizardData,
            ...data,
        };
    };

    getMutatedStateForStep = (
        stepName: string,
    ): Partial<CreateRiskCustomMutationType> => {
        if (!this.createRiskWizardData) {
            return {};
        }

        switch (stepName) {
            case 'sourceAndStatus': {
                return {
                    riskSourceGroup: this.createRiskWizardData.riskSourceGroup,
                    statusGroup: this.createRiskWizardData.statusGroup,
                };
            }
            case 'details': {
                return {
                    riskDetailsGroup:
                        this.createRiskWizardData.riskDetailsGroup,
                };
            }
            case 'assessmentAndTreatment': {
                return {
                    inherentScoreGroup:
                        this.createRiskWizardData.inherentScoreGroup,
                    treatmentGroup: this.createRiskWizardData.treatmentGroup,
                };
            }
            default: {
                return {};
            }
        }
    };

    setMappedControls = (controls: ListBoxItemData[]) => {
        this.mappedControls = controls;
    };

    resetProcess = () => {
        this.risk = null;
        this.uploadStatus = 'NOT_STARTED';
        this.mutatedRiskDetails = null;
        this.createRiskWizardData = null;
    };

    createMutatedRiskDetails = (vendorId?: number) => {
        const formValues = toJS(this.createRiskWizardData) ?? {};
        const status = formValues.statusGroup?.status.value ?? 'ACTIVE';
        const controls = getAdaptedControls(
            sharedLinkControlsController.selectedControls as AuditFrameworkControlResponseDto[],
        );
        const identifiedAt = formValues.riskDetailsGroup?.identifiedAt
            ? formatDate('timestamp', formValues.riskDetailsGroup.identifiedAt)
            : undefined;
        const categories = formValues.riskDetailsGroup?.categories
            ? getUniqueIdsList(formValues.riskDetailsGroup.categories)
            : undefined;
        const owners = formValues.riskDetailsGroup?.owners
            ? getUniqueIdsList(formValues.riskDetailsGroup.owners)
            : undefined;
        const impact = getInherentValue(formValues, 'impact');
        const likelihood = getInherentValue(formValues, 'likelihood');
        const score = getScore(formValues);
        const residualScore = getResidualScore(formValues.treatmentGroup);
        const residualImpact = formValues.treatmentGroup
            ? getResidualValue(formValues.treatmentGroup, 'residualImpact')
            : undefined;
        const residualLikelihood = formValues.treatmentGroup
            ? getResidualValue(formValues.treatmentGroup, 'residualLikelihood')
            : undefined;
        const treatmentPlan =
            formValues.treatmentGroup?.treatment?.value ?? 'UNTREATED';
        const treatmentDetails =
            formValues.treatmentGroup?.treatmentPlan ?? undefined;
        const reviewers = formValues.treatmentGroup?.reviewers
            ? getUniqueIdsList(formValues.treatmentGroup.reviewers)
            : undefined;
        const anticipatedCompletionDate = formValues.treatmentGroup
            ?.anticipatedCompletionDate
            ? formatDate(
                  'timestamp',
                  formValues.treatmentGroup.anticipatedCompletionDate,
              )
            : undefined;
        const completionDate = formValues.treatmentGroup?.completedDate
            ? formatDate('timestamp', formValues.treatmentGroup.completedDate)
            : undefined;

        this.mutatedRiskDetails = zRiskRequestDto.parse({
            type: 'INTERNAL',
            status,
            identifiedAt,
            vendorId,
            categories,
            owners,
            impact,
            likelihood,
            score,
            residualScore,
            residualImpact,
            residualLikelihood,
            treatmentPlan,
            treatmentDetails,
            controls,
            title: formValues.riskDetailsGroup?.title,
            description: formValues.riskDetailsGroup?.description,
            reviewers,
            anticipatedCompletionDate,
            completionDate,
        });
    };

    createNewRisk = (vendorId?: number) => {
        if (this.uploadStatus === 'IN_PROGRESS') {
            return;
        }
        this.uploadStatus = 'IN_PROGRESS';

        this.createMutatedRiskDetails(vendorId);

        if (isNil(this.risk)) {
            this.createRiskMutation.mutate({
                body: {
                    ...toJS(this.mutatedRiskDetails),
                    vendorId,
                    title: this.mutatedRiskDetails?.title || '',
                    description: this.mutatedRiskDetails?.description || '',
                    treatmentPlan:
                        this.mutatedRiskDetails?.treatmentPlan || 'UNTREATED',
                },
            });
        }

        when(
            () => !this.createRiskMutation.isPending,
            () => {
                if (this.createRiskMutation.hasError) {
                    this.uploadStatus = 'ERROR';

                    snackbarController.addSnackbar({
                        id: 'create-risk-error',
                        props: {
                            title: t`Error creating risk data`,
                            description: t`An error occurred while creating the risk data. Try again later.",
                            severity: "critical`,
                            closeButtonAriaLabel: 'Close',
                        },
                    });

                    return;
                }

                const { response } = this.createRiskMutation;

                this.risk = response;

                this.mayUploadRiskFiles(response);
            },
        );

        when(
            () => this.uploadStatus === 'SUCCESS',
            () => {
                snackbarController.addSnackbar({
                    id: 'create-risk-success',
                    props: {
                        title: t`Risk created`,
                        description: t`The risk was created successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: 'Close',
                    },
                });
            },
        );
    };

    mayUploadRiskFiles = (response: RiskResponseDto | null) => {
        const formValues = toJS(this.createRiskWizardData) ?? {};

        if (
            !isEmpty(formValues.riskDetailsGroup?.documents) &&
            response?.riskId
        ) {
            if (Array.isArray(formValues.riskDetailsGroup?.documents)) {
                this.uploadRiskFiles(
                    response.riskId,
                    formValues.riskDetailsGroup.documents as unknown as File[],
                );
            }
        } else {
            this.uploadStatus = 'SUCCESS';
        }
    };

    uploadRiskFiles = (riskId: string, files: File[]) => {
        this.vendorRiskUploadDocumentMutation.mutate({
            body: {
                files,
            },
            path: { risk_id: riskId },
        });

        when(
            () => !this.vendorRiskUploadDocumentMutation.isPending,
            () => {
                if (this.vendorRiskUploadDocumentMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'create-risk-upload-error',
                        props: {
                            title: t`Error uploading risk supporting documents`,
                            description: t`An error occurred while uploading the supporting documents. Try again later.",
                            severity: "critical`,
                            closeButtonAriaLabel: 'Close',
                        },
                    });
                }
                // Setting status to success because the Risk was created in spite of the error
                if (this.uploadStatus !== 'SUCCESS') {
                    this.uploadStatus = 'SUCCESS';
                }
            },
        );
    };

    get isCreating(): boolean {
        return this.createRiskMutation.isPending;
    }

    get hasError(): Error | null {
        return this.createRiskMutation.error;
    }

    createRisk = (data: RiskRequestDto) => {
        this.createRiskMutation.mutate({
            body: data,
        });
    };
}

export const sharedCreateRiskMutationController =
    new CreateRiskMutationController();
