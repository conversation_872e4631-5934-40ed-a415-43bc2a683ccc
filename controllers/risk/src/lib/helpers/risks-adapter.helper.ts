import { isNaN, uniqBy } from 'lodash-es';
import type { CreateRiskCustomMutationType } from '@controllers/risk';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import type {
    AuditFrameworkControlResponseDto,
    ControlListResponseDto,
} from '@globals/api-sdk/types';

export const getUniqueIdsList = (
    selectedItems: ListBoxItemData[],
): {
    id: number;
}[] => {
    return uniqBy(
        selectedItems.map((item) => ({ id: Number(item.id) })),
        'id',
    );
};

export const getInherentValue = (
    values: CreateRiskCustomMutationType,
    field: 'impact' | 'likelihood',
): number | undefined => {
    const { inherentScoreGroup } = values;

    const valueStr = inherentScoreGroup?.[field]?.value;

    const numericValue = Number(valueStr);

    return isNaN(numericValue) ? undefined : numericValue;
};

export const getResidualValue = (
    values: NonNullable<CreateRiskCustomMutationType['treatmentGroup']>,
    field: 'residualImpact' | 'residualLikelihood',
): number | undefined => {
    const { residualScoreGroup } = values;

    const valueStr = residualScoreGroup?.[field]?.value;

    const numericValue = Number(valueStr);

    return isNaN(numericValue) ? undefined : numericValue;
};

export const getScore = (
    values: CreateRiskCustomMutationType,
): number | undefined => {
    const impact = getInherentValue(values, 'impact');

    const likelihood = getInherentValue(values, 'likelihood');

    if (!impact || !likelihood) {
        return undefined;
    }

    return Number(impact) * Number(likelihood);
};

export const getResidualScore = (
    values: CreateRiskCustomMutationType['treatmentGroup'],
): number | undefined => {
    const residualImpact = values
        ? getResidualValue(values, 'residualImpact')
        : undefined;

    const residualLikelihood = values
        ? getResidualValue(values, 'residualLikelihood')
        : undefined;

    if (!residualImpact || !residualLikelihood) {
        return undefined;
    }

    return Number(residualImpact) * Number(residualLikelihood);
};

export const getAdaptedControls = (
    controls: (ControlListResponseDto | AuditFrameworkControlResponseDto)[],
): {
    id: number;
}[] => {
    return Array.isArray(controls)
        ? controls.map((control) => ({
              id: Number(control.id),
          }))
        : [];
};
