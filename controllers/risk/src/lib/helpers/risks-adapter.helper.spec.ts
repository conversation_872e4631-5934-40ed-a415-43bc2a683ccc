import { describe, expect, test } from 'vitest';
import type { CreateRiskCustomMutationType } from '@controllers/risk';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import {
    getResidualScore,
    getScore,
    getUniqueIdsList,
} from './risks-adapter.helper';

describe('getAdaptedControls', () => {
    describe('getUniqueIdsList', () => {
        test('should return empty array when input is empty', () => {
            const result = getUniqueIdsList([]);

            expect(result).toStrictEqual([]);
        });

        test('should return unique IDs from list box items', () => {
            const items: ListBoxItemData[] = [
                { id: '1', label: 'Item 1' },
                { id: '2', label: 'Item 2' },
                { id: '3', label: 'Item 3' },
            ];

            const result = getUniqueIdsList(items);

            expect(result).toStrictEqual([{ id: 1 }, { id: 2 }, { id: 3 }]);
        });

        test('should remove duplicate IDs', () => {
            const items: ListBoxItemData[] = [
                { id: '1', label: 'Item 1' },
                { id: '2', label: 'Item 2' },
                { id: '1', label: 'Item 1 Duplicate' },
                { id: '3', label: 'Item 3' },
                { id: '2', label: 'Item 2 Duplicate' },
            ];

            const result = getUniqueIdsList(items);

            expect(result).toStrictEqual([{ id: 1 }, { id: 2 }, { id: 3 }]);
        });

        test('should handle string IDs by converting them to numbers', () => {
            const items: ListBoxItemData[] = [
                { id: '1', label: 'Item 1' },
                { id: '2.5', label: 'Item 2.5' },
            ];

            const result = getUniqueIdsList(items);

            expect(result).toStrictEqual([{ id: 1 }, { id: 2.5 }]);
        });

        test('should handle mixed numeric and string IDs', () => {
            // Create properly typed mixed items
            const items: ListBoxItemData[] = [
                { id: '1', label: 'Item 1' },
                { id: '2', label: 'Item 2' },
            ];

            // Override first item's id to be a number for test purposes
            (items[0] as unknown as { id: number }).id = 1;

            const result = getUniqueIdsList(items);

            expect(result).toStrictEqual([{ id: 1 }, { id: 2 }]);
        });

        test('should maintain order of first occurrence when removing duplicates', () => {
            const items: ListBoxItemData[] = [
                { id: '3', label: 'Item 3' },
                { id: '1', label: 'Item 1' },
                { id: '2', label: 'Item 2' },
                { id: '1', label: 'Item 1 Duplicate' },
            ];

            const result = getUniqueIdsList(items);

            expect(result).toStrictEqual([{ id: 3 }, { id: 1 }, { id: 2 }]);
        });
    });

    describe('score calculation helpers', () => {
        describe('getScore', () => {
            test('should calculate score as impact * likelihood', () => {
                const mockValues: CreateRiskCustomMutationType = {
                    inherentScoreGroup: {
                        impact: {
                            id: '3',
                            label: 'High',
                            value: '3',
                        },
                        likelihood: {
                            id: '4',
                            label: 'Likely',
                            value: '4',
                        },
                    },
                };

                const result = getScore(mockValues);

                expect(result).toBe(12); // 3 * 4 = 12
            });

            test('should return null when impact is missing', () => {
                const mockValues: CreateRiskCustomMutationType = {
                    inherentScoreGroup: {
                        likelihood: {
                            id: '4',
                            label: 'Likely',
                            value: '4',
                        },
                    },
                };

                const result = getScore(mockValues);

                expect(result).toBeUndefined();
            });

            test('should return null when likelihood is missing', () => {
                const mockValues: CreateRiskCustomMutationType = {
                    inherentScoreGroup: {
                        impact: {
                            id: '3',
                            label: 'High',
                            value: '3',
                        },
                    },
                };

                const result = getScore(mockValues);

                expect(result).toBeUndefined();
            });

            test('should return null when both impact and likelihood are missing', () => {
                const mockValues: CreateRiskCustomMutationType = {
                    inherentScoreGroup: {},
                };

                const result = getScore(mockValues);

                expect(result).toBeUndefined();
            });

            test('should return null when inherentScoreGroup is missing', () => {
                const mockValues: CreateRiskCustomMutationType = {};

                const result = getScore(mockValues);

                expect(result).toBeUndefined();
            });

            test('should handle zero values correctly, returning null', () => {
                const mockValues: CreateRiskCustomMutationType = {
                    inherentScoreGroup: {
                        impact: {
                            id: '0',
                            label: 'None',
                            value: '0',
                        },
                        likelihood: {
                            id: '5',
                            label: 'Very Likely',
                            value: '5',
                        },
                    },
                };

                const result = getScore(mockValues);

                expect(result).toBeUndefined();
            });

            test('should handle non-numeric values', () => {
                const mockValues: CreateRiskCustomMutationType = {
                    inherentScoreGroup: {
                        impact: {
                            id: 'invalid',
                            label: 'Invalid',
                            value: 'invalid',
                        },
                        likelihood: {
                            id: '5',
                            label: 'Very Likely',
                            value: '5',
                        },
                    },
                };

                const result = getScore(mockValues);

                expect(result).toBeUndefined();
            });

            test('should handle decimal values correctly', () => {
                const mockValues: CreateRiskCustomMutationType = {
                    inherentScoreGroup: {
                        impact: {
                            id: '2.5',
                            label: 'Medium-High',
                            value: '2.5',
                        },
                        likelihood: {
                            id: '3',
                            label: 'Moderate',
                            value: '3',
                        },
                    },
                };

                const result = getScore(mockValues);

                expect(result).toBe(7.5); // 2.5 * 3 = 7.5
            });
        });

        describe('getResidualScore', () => {
            test('should calculate residual score as residualImpact * residualLikelihood', () => {
                const mockValues: NonNullable<
                    CreateRiskCustomMutationType['treatmentGroup']
                > = {
                    residualScoreGroup: {
                        residualImpact: {
                            value: '2',
                            label: 'Medium',
                            id: '2',
                        },
                        residualLikelihood: {
                            value: '3',
                            label: 'Likely',
                            id: '3',
                        },
                    },
                };

                const result = getResidualScore(mockValues);

                expect(result).toBe(6); // 2 * 3 = 6
            });

            test('should return null when treatmentGroup is missing', () => {
                const mockValues: CreateRiskCustomMutationType['treatmentGroup'] =
                    undefined;

                const result = getResidualScore(mockValues);

                expect(result).toBeUndefined();
            });

            test('should return null when residualImpact is missing', () => {
                const mockValues: NonNullable<
                    CreateRiskCustomMutationType['treatmentGroup']
                > = {
                    residualScoreGroup: {
                        residualLikelihood: {
                            value: '3',
                            label: 'Likely',
                            id: '3',
                        },
                    },
                };

                const result = getResidualScore(mockValues);

                expect(result).toBeUndefined();
            });

            test('should return null when residualLikelihood is missing', () => {
                const mockValues: NonNullable<
                    CreateRiskCustomMutationType['treatmentGroup']
                > = {
                    residualScoreGroup: {
                        residualImpact: {
                            value: '2',
                            label: 'Medium',
                            id: '2',
                        },
                    },
                };

                const result = getResidualScore(mockValues);

                expect(result).toBeUndefined();
            });

            test('should return null when both residualImpact and residualLikelihood are missing', () => {
                const mockValues: NonNullable<
                    CreateRiskCustomMutationType['treatmentGroup']
                > = {
                    residualScoreGroup: {},
                };

                const result = getResidualScore(mockValues);

                expect(result).toBeUndefined();
            });

            test('should return null when residualScoreGroup is missing', () => {
                const mockValues: NonNullable<
                    CreateRiskCustomMutationType['treatmentGroup']
                > = {};

                const result = getResidualScore(mockValues);

                expect(result).toBeUndefined();
            });

            test('should handle zero values correctly, returning null', () => {
                const mockValues: NonNullable<
                    CreateRiskCustomMutationType['treatmentGroup']
                > = {
                    residualScoreGroup: {
                        residualImpact: { value: '0', label: 'None', id: '0' },
                        residualLikelihood: {
                            value: '4',
                            label: 'Likely',
                            id: '4',
                        },
                    },
                };

                const result = getResidualScore(mockValues);

                expect(result).toBeUndefined();
            });

            test('should handle non-numeric values', () => {
                const mockValues: NonNullable<
                    CreateRiskCustomMutationType['treatmentGroup']
                > = {
                    residualScoreGroup: {
                        residualImpact: {
                            value: '2',
                            label: 'Medium',
                            id: '2',
                        },
                        residualLikelihood: {
                            value: 'invalid',
                            label: 'Invalid',
                            id: 'invalid',
                        },
                    },
                };

                const result = getResidualScore(mockValues);

                expect(result).toBeUndefined();
            });

            test('should handle string numeric values correctly', () => {
                const mockValues: NonNullable<
                    CreateRiskCustomMutationType['treatmentGroup']
                > = {
                    residualScoreGroup: {
                        residualImpact: { value: '4', label: 'High', id: '4' },
                        residualLikelihood: {
                            value: '2',
                            label: 'Medium',
                            id: '2',
                        },
                    },
                };

                const result = getResidualScore(mockValues);

                expect(result).toBe(8); // 4 * 2 = 8
            });

            test('should handle decimal values correctly', () => {
                const mockValues: NonNullable<
                    CreateRiskCustomMutationType['treatmentGroup']
                > = {
                    residualScoreGroup: {
                        residualImpact: {
                            value: '2.5',
                            label: 'Medium-High',
                            id: '2.5',
                        },
                        residualLikelihood: {
                            value: '3',
                            label: 'Moderate',
                            id: '3',
                        },
                    },
                };

                const result = getResidualScore(mockValues);

                expect(result).toBe(7.5); // 2.5 * 3 = 7.5
            });
        });
    });
});
