import { isNil, isNumber } from 'lodash-es';
import {
    sharedAuditHubAuditController,
    sharedAuditHubController,
} from '@controllers/audit-hub';
import {
    sharedAuditHubAuditorClientEvidenceStatusController,
    sharedCompanyArchiveStatusQueryController,
} from '@controllers/audit-hub-auditor-client-actions';
import { sharedCustomerRequestsController } from '@controllers/customer-requests';
import { snackbarController } from '@controllers/snackbar';
import { companiesControllerGetLatestCompanyArchiveOptions } from '@globals/api-sdk/queries';
import type { SignedUrlResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';

class AuditHubAuditorClientMethodsRequestPackageController {
    getLatestCompanyArchiveQuery = new ObservedQuery(
        companiesControllerGetLatestCompanyArchiveOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    downloadPreAuditPackage(
        category: 'PRE_AUDIT' | 'CONTROL_EVIDENCE',
        status?: 'PENDING' | 'SUCCESS' | 'FAILED',
    ): void {
        when(
            () =>
                !isNil(
                    sharedAuditHubController.auditByIdData?.framework.productId,
                ),
            () => {
                if (
                    !isNumber(
                        sharedAuditHubController.auditByIdData?.framework
                            .productId,
                    )
                ) {
                    return;
                }
                this.getLatestCompanyArchiveQuery.load({
                    path: {
                        xProductId:
                            sharedAuditHubController.auditByIdData.framework
                                .productId,
                    },
                    query: {
                        category,
                        status,
                        auditFrameworkId:
                            sharedCustomerRequestsController.auditId,
                    },
                });
                if (category === 'PRE_AUDIT') {
                    sharedAuditHubAuditController.isPreAuditPackageReady = true;
                }
            },
        );

        when(
            () => this.getLatestCompanyArchiveQuery.hasError,
            () => {
                snackbarController.addSnackbar({
                    id: 'latest-company-archive-query-error',
                    props: {
                        title: t`Company Archive Error`,
                        description: t`Failed to load company archive data. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );

        when(
            () => !this.getLatestCompanyArchiveQuery.isLoading,
            () => {
                if (isNil(this.preAuditPackage?.signedUrl)) {
                    return;
                }
                snackbarController.addSnackbar({
                    id: 'success-download-pre-audit-package',
                    props: {
                        title: t` Download is ready.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
                window.open(
                    this.preAuditPackage.signedUrl,
                    '_self',
                    'noopener, noreferrer',
                );
            },
        );
    }

    get preAuditPackage(): SignedUrlResponseDto | null {
        return this.getLatestCompanyArchiveQuery.data;
    }

    get downloadPreAuditPackageIsLoading(): boolean {
        return this.getLatestCompanyArchiveQuery.isLoading;
    }

    generatePreAuditPackage() {
        when(
            () =>
                !sharedAuditHubController.auditByIdIsLoading &&
                !isNil(sharedCustomerRequestsController.auditId),
            () => {
                if (
                    !isNumber(
                        sharedAuditHubController.auditByIdData?.framework
                            .productId,
                    )
                ) {
                    return;
                }
                // flag to set proper label on pre audit package options
                sharedAuditHubAuditController.isPreAuditPackageReady = true;

                sharedAuditHubAuditController.loadAllCompanies(
                    sharedAuditHubController.auditByIdData.framework.productId,
                );

                sharedCompanyArchiveStatusQueryController.loadLatestCompanyArchiveStatus(
                    'PRE_AUDIT',
                );
                // evidence expired status must be updated
                sharedAuditHubAuditorClientEvidenceStatusController.updateEvidenceExpiredStatusByArchiveStatus();
            },
        );
    }
}

export const sharedAuditHubAuditorClientMethodsRequestPackageController =
    new AuditHubAuditorClientMethodsRequestPackageController();
