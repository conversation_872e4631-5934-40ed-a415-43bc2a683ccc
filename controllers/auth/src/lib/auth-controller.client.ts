import { isNil } from 'lodash-es';
import { sharedAnalyticsController } from '@globals/analytics';
import { client } from '@globals/api-sdk';
import {
    type AuthOptions,
    type Regions,
    sharedConfigController,
} from '@globals/config';
import { sharedCurrentCompanyController } from '@globals/current-company';
import { sharedCurrentUserController } from '@globals/current-user';
import {
    action,
    autorun,
    makeAutoObservable,
    reaction,
    runInAction,
    when,
} from '@globals/mobx';
import { AUTHENTICATOR_STORAGE_KEY } from './constants';
import {
    createAuthenticatorByType,
    createAuthenticatorFromPersistedState,
    parsePersistedState,
} from './helpers/authenticator-factory.client';
import { sharedRefreshTokenController } from './refresh-token-controller.client';
import type { AuthModes, AuthType } from './types';
import type { Authenticator } from './types/auth.types';

class AuthController {
    /**
     * Using the underscore to indicate that this is a private property.
     * If you find yourself accessing directly to this property, you are probably doing something wrong.
     * You should be writing a method to wrap such logic. The authenticator is using the composite pattern
     * so the concrete object might change over time without you knowing.
     * This is why is not recommended to use it directly.
     */
    _authenticator: Authenticator;
    hasFailedLogin = false;

    constructor() {
        makeAutoObservable(this);
        this._authenticator = this.hydrateAuthenticatorFromStorage();
    }

    /**
     * This method adds an event listener to the local storage of the browser to listen to changes in the authenticator state.
     * This is useful for two main purposes:
     * 1. When the user has multiple tabs open and he logs in into one of them, this will perceive the update to the authenticator
     * state and redirect the app to the user's home page in the other tabs.
     * 2. When the user has multiple tabs open and he logs out in one of them, this will perceive the update to the authenticator
     * state and redirect the app to the login page in the other tabs.
     */
    addLoginFlowEventListener(): void {
        // eslint-disable-next-line custom/no-direct-dom-manipulation -- Required for cross-tab authentication state synchronization
        window.addEventListener('storage', (event) => {
            if (event.key !== AUTHENTICATOR_STORAGE_KEY) {
                return;
            }

            const previousAuthenticatorState = parsePersistedState(
                event.oldValue ?? '',
            );
            const currentAuthenticatorState = parsePersistedState(
                event.newValue ?? '',
            );

            const hasLoggedIn =
                !previousAuthenticatorState?.accessToken &&
                currentAuthenticatorState?.accessToken;
            const hasLoggedOut =
                previousAuthenticatorState?.accessToken &&
                !currentAuthenticatorState?.accessToken;

            if (hasLoggedIn) {
                runInAction(() => {
                    this._authenticator =
                        createAuthenticatorFromPersistedState();
                });
            }

            if (hasLoggedOut) {
                runInAction(() => {
                    this.logout();
                });
            }
        });
    }

    init() {
        if (this._authenticator.token) {
            // Set the client auth header to the token. Since it might be set already but we haven't set auth header yet.
            this.setClientAuthHeader(this._authenticator.token);
            // If we have a stored access token it might be a good idea to refresh the token as it might be expired.
            // Commenting this out for now to unblock Beacon-UI. Leaving for now if we need it in the future.
            // sharedRefreshTokenController.refreshAccessToken(
            //     sharedCurrentCompanyController.company?.accountId,
            // );
            sharedRefreshTokenController.enableRefreshTokenInterval();
        }

        this.addLoginFlowEventListener();

        // persist the authenticator to local storage everytime the access token changes. This is due to the refresh token mechanism.
        autorun(() => {
            if (!this._authenticator.token) {
                return;
            }

            const persistableState = this._authenticator.getPersistableState();

            this._authenticator.persistState(persistableState);
            this.setClientAuthHeader(this._authenticator.token);
            sharedRefreshTokenController.enableRefreshTokenInterval();
        });

        reaction(
            () => this._authenticator.region,
            (region) => {
                if (!region) {
                    return;
                }

                this.updateClientApiUrl(region);
            },
        );
    }

    hydrateAuthenticatorFromStorage(): Authenticator {
        // check local storage for information about the authenticator.
        const hydratedAuthenticator = createAuthenticatorFromPersistedState();

        console.debug({ hydratedAuthenticator });

        return hydratedAuthenticator;
    }

    get isUserAuthenticated(): boolean {
        return !isNil(this._authenticator.token);
    }

    get authMode(): AuthModes | null {
        return this._authenticator.authMode;
    }

    get redirectionLink(): string {
        return (
            this._authenticator.initialRedirectionUrl ?? 'compliance/frameworks'
        );
    }

    get isAttemptingLogin(): boolean {
        return this._authenticator.isAttemptingLogin;
    }

    get hasAttemptedLoginError(): boolean {
        return this._authenticator.hasAttemptedLoginError;
    }

    get hasAttemptedLogin(): boolean {
        return this._authenticator.hasAttemptedLogin;
    }

    get isLoggingIn(): boolean {
        return this._authenticator.loginMagicLinkMutation?.isPending ?? false;
    }

    get email(): string | null {
        return this._authenticator.email || null;
    }

    get region(): Regions | null {
        return this._authenticator.region;
    }

    get accessToken(): string | null {
        return this._authenticator.token || null;
    }

    get auditorClientAuthenticator(): Authenticator | null {
        const { auditorClientAuthenticator } = this._authenticator;

        return auditorClientAuthenticator ?? null;
    }

    get isAcceptTermsPending(): boolean {
        return this._authenticator.isAcceptTermsPending;
    }

    get hasAcceptTermsError(): boolean {
        return Boolean(this._authenticator.hasAcceptTermsError);
    }

    get hasAcceptTermsSuccess(): boolean {
        return Boolean(this._authenticator.hasAcceptTermsSuccess);
    }

    /**
     * It updates the email in the authenticator. If the email has changed and is different from
     * the current one, it will logout the user and create a new authenticator. This ensures that
     * the user is not logged in with a different email.
     */
    updateEmail(email: string): void {
        if (
            this._authenticator.email !== null &&
            this._authenticator.email !== email
        ) {
            this.logout();
        }

        this._authenticator.setEmail(email);
    }

    updateClientApiUrl(region: Regions): void {
        const baseUrl = this.getUrlFromConfigController(region);

        client.setConfig({
            baseUrl,
        });
    }

    getUrlFromConfigController(region: Regions): string {
        // Commented code for future implementation
        // sharedConfigController.switchRegion(region);
        console.debug('Getting api url for the region: ', region);

        return (
            sharedConfigController.configs.url?.api ?? 'http://localhost:3000'
        );
    }

    updateAccessToken(accessToken: string | null): void {
        this._authenticator.setAccessToken(accessToken || null);
    }

    updateAuthMode(authMode: AuthModes): void {
        this._authenticator = this._authenticator.setAuthMode(authMode);
    }

    updateAuthType(authType: AuthType): void {
        this._authenticator.setAuthType(authType);
    }

    updateRegion(region: Regions): void {
        this._authenticator.setRegion(region);
    }

    attemptLogin(email: string, region: Regions, options?: AuthOptions): void {
        this.updateEmail(email);

        this.updateClientApiUrl(region);

        this._authenticator.attemptLogin(region, options);
    }

    resendMagicLink(): void {
        this._authenticator.resendMagicLinkEmail();
    }

    finalizeLogin(code: string): void {
        this._authenticator.finalizeLogin(code);
    }

    acceptTermsAndConditions(): void {
        this._authenticator.acceptTermsAndConditions();
    }

    /**
     * Finalizes login from a legacy system.
     * This method is used when a user logs in through the old app and is redirected to the new app
     * with a token, email, and region. It updates the current authenticator with the provided
     * information and then calls finalizeLogin with the token to complete the authentication process.
     */
    finalizeLoginFromLegacy(
        token: string,
        email: string,
        region: Regions,
    ): void {
        try {
            this.updateAuthMode('NORMAL');
            this.updateEmail(email);
            this.updateAuthType('MAGIC_LINK');
            this.updateRegion(region);

            this.finalizeLogin(token);
        } catch (error) {
            // TODO use logger
            console.error('Error finalizing login from legacy system:', error);

            this.hasFailedLogin = true;
        }
    }

    setClientAuthHeader(accessToken: string | null): void {
        client.setConfig({
            headers: {
                Authorization: `Bearer ${accessToken}`,
            },
        });
    }

    logout(): void {
        sharedRefreshTokenController.disableRefreshTokenInterval();
        sharedRefreshTokenController.invalidateRefreshToken();
        sharedCurrentUserController.unload();
        sharedCurrentCompanyController.unload();
        sharedAnalyticsController.destroy();
        // This should delete everything from storage.
        this._authenticator.logout();
        // This should now reinitialize everything to the builder.
        this._authenticator = createAuthenticatorByType();
    }
}

export const sharedAuthController = new AuthController();

runInAction(() => {
    sharedAuthController.init();
});

client.interceptors.response.use(
    action((response, request) => {
        if (response.status === 401) {
            if (request.url.endsWith('/auth/refresh-token')) {
                console.error('WOULD HAVE LOGGED OUT');
                sharedAuthController.logout();

                return response;
            }

            if (sharedAuthController._authenticator.token) {
                sharedRefreshTokenController.refreshAccessToken(
                    sharedCurrentCompanyController.company?.accountId,
                );

                return response;
            }
        }

        return response;
    }),
);

reaction(
    () => sharedRefreshTokenController.refreshTokenQuery.data?.accessToken,
    (newAccessToken) => {
        if (newAccessToken) {
            sharedAuthController.updateAccessToken(newAccessToken);
        }
    },
);

// TODO: look into this, its not the most clean way to do it but we are good.
// Also this needs to start taking into account the auditors.
when(() => sharedAuthController.isUserAuthenticated, {
    name: 'auth loader',
})
    .then(
        action(() => {
            sharedCurrentUserController.load(sharedAuthController.authMode);
            sharedCurrentCompanyController.load();
            sharedAnalyticsController.startup();
        }),
    )
    // eslint-disable-next-line unicorn/prefer-top-level-await -- not for this one
    .catch(
        action((error) => {
            // TODO - handle errors
            console.error(error);
        }),
    );

/**
 * Add email to authenticator when user is logged in through invitation link
 */
when(
    () =>
        !!sharedCurrentUserController.user?.email &&
        !sharedAuthController.email,
    {
        name: 'current user loader',
    },
).then(
    action(() => {
        const userEmail = sharedCurrentUserController.user?.email;
        const authMode = sharedAuthController.authMode;
        if (userEmail && authMode) {
            sharedAuthController._authenticator.setEmail(userEmail);
            // instantiate new authenticator depending on authMode
            sharedAuthController.updateAuthMode(authMode);
            const persistableState =
                sharedAuthController._authenticator.getPersistableState();
            sharedAuthController._authenticator.persistState(persistableState);
        }
    }),
);

reaction(
    () => sharedAuthController.accessToken,
    (newAccessToken) => {
        if (
            !newAccessToken ||
            !sharedAuthController.auditorClientAuthenticator
        ) {
            return;
        }

        sharedCurrentCompanyController.load();
        sharedCurrentUserController.load(sharedAuthController.authMode);
    },
);
