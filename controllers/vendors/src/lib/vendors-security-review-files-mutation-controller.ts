import { uniqueId } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import {
    schedulesFellowUpReminderControllerSendReminderMutation,
    vendorsDocumentsControllerGetVendorDocumentDownloadUrlOptions,
    vendorsSecurityReviewsControllerDeleteSecurityReviewDocumentMutation,
} from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    runInAction,
    when,
} from '@globals/mobx';
import { downloadFileFromSignedUrl } from '@helpers/download-file';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { sharedVendorsDetailsController } from './vendors-details-controller';
import { sharedVendorsSecurityReviewDocumentsController } from './vendors-security-review-documents-controller';

export class VendorsSecurityReviewFilesMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    downloadUrlQuery = new ObservedQuery(
        vendorsDocumentsControllerGetVendorDocumentDownloadUrlOptions,
    );

    deleteFileMutation = new ObservedMutation(
        vendorsSecurityReviewsControllerDeleteSecurityReviewDocumentMutation,
        {
            onSuccess: () => {
                sharedVendorsSecurityReviewDocumentsController.securityReviewDocumentsQuery.invalidate();
            },
        },
    );

    sendReminderMutation = new ObservedMutation(
        schedulesFellowUpReminderControllerSendReminderMutation,
        {
            onSuccess: () => {
                snackbarController.addSnackbar({
                    id: `reminder-sent-success-${uniqueId()}`,
                    hasTimeout: true,
                    props: {
                        title: t`Reminder email sent.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: `reminder-sent-error-${uniqueId()}`,
                    props: {
                        title: t`Unable to send reminder email.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    get isDownloadPending(): boolean {
        return this.downloadUrlQuery.isLoading;
    }

    get isDeletePending(): boolean {
        return this.deleteFileMutation.isPending;
    }

    get isSendReminderPending(): boolean {
        return this.sendReminderMutation.isPending;
    }

    downloadFile = (documentId: number): void => {
        const { vendorDetails } = sharedVendorsDetailsController;

        if (!vendorDetails?.id || !documentId) {
            return;
        }

        this.downloadUrlQuery.load({
            path: { id: vendorDetails.id, docId: documentId },
        });

        when(
            () => !this.downloadUrlQuery.isLoading,
            () => {
                const downloadData = this.downloadUrlQuery.data;

                if (this.downloadUrlQuery.error) {
                    logger.error({
                        message: 'Failed to download security review file:',
                        additionalInfo: {
                            error: this.downloadUrlQuery.error,
                            documentId,
                        },
                    });
                    snackbarController.addSnackbar({
                        id: `security-review-file-download-error-${uniqueId()}`,
                        props: {
                            title: t`Failed to download file`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                if (downloadData?.signedUrl) {
                    downloadFileFromSignedUrl(downloadData.signedUrl);

                    snackbarController.addSnackbar({
                        id: `security-review-file-download-success-${uniqueId()}`,
                        hasTimeout: true,
                        props: {
                            title: t`File download started`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: `security-review-file-download-error-${uniqueId()}`,
                    props: {
                        title: t`Unable to get download URL`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    deleteFile = (securityReviewDocumentId: number): void => {
        this.deleteFileMutation.mutate({
            path: { id: securityReviewDocumentId },
        });

        when(
            () => !this.isDeletePending,
            () => {
                if (this.deleteFileMutation.hasError) {
                    logger.error({
                        message: 'Failed to delete security review file:',
                        additionalInfo: {
                            error: this.deleteFileMutation.error,
                            securityReviewDocumentId,
                        },
                    });

                    snackbarController.addSnackbar({
                        id: `security-review-file-delete-error-${uniqueId()}`,
                        props: {
                            title: t`Failed to delete file`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: `security-review-file-delete-success-${uniqueId()}`,
                    hasTimeout: true,
                    props: {
                        title: t`File deleted successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    deleteQuestionnaire = (
        securityReviewQuestionnaireDocumentId: number,
    ): void => {
        this.deleteFileMutation.mutate({
            path: { id: securityReviewQuestionnaireDocumentId },
        });

        when(
            () => !this.isDeletePending,
            () => {
                if (this.deleteFileMutation.hasError) {
                    logger.error({
                        message:
                            'Failed to delete security review questionnaire:',
                        additionalInfo: {
                            error: this.deleteFileMutation.error,
                            securityReviewQuestionnaireDocumentId,
                        },
                    });

                    snackbarController.addSnackbar({
                        id: `security-review-questionnaire-delete-error-${uniqueId()}`,
                        props: {
                            title: t`Failed to delete questionnaire`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: `security-review-questionnaire-delete-success-${uniqueId()}`,
                    hasTimeout: true,
                    props: {
                        title: t`Questionnaire deleted successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    openDeleteConfirmationModal = (
        securityReviewDocumentId: number,
        fileName: string,
    ): void => {
        const handleOnConfirm = () => {
            runInAction(() => {
                this.deleteFile(securityReviewDocumentId);
            });
            closeConfirmationModal();
        };

        openConfirmationModal({
            title: t`Delete File`,
            body: t`Confirm that you'd like to delete ${fileName} file.`,
            confirmText: t`Yes, delete file`,
            cancelText: t`Cancel`,
            type: 'danger',
            size: 'md',
            onConfirm: handleOnConfirm,
            onCancel: () => {
                closeConfirmationModal();
            },
        });
    };

    openDeleteQuestionnaireConfirmationModal = (
        securityReviewQuestionnaireDocumentId: number,
    ): void => {
        const handleOnConfirm = () => {
            runInAction(() => {
                this.deleteQuestionnaire(securityReviewQuestionnaireDocumentId);
            });
            closeConfirmationModal();
        };

        openConfirmationModal({
            title: t`Delete Questionnaire`,
            body: t`Confirm that you'd like to delete the questionnaire.`,
            confirmText: t`Yes, delete questionnaire`,
            cancelText: t`No, take me back`,
            type: 'danger',
            size: 'md',
            onConfirm: handleOnConfirm,
            onCancel: () => {
                closeConfirmationModal();
            },
        });
    };

    sendReminder = async (questionnaireId: number): Promise<void> => {
        if (this.isSendReminderPending || !questionnaireId) {
            return;
        }

        try {
            await this.sendReminderMutation.mutateAsync({
                body: { questionnaireId },
            });
        } catch (error) {
            // Error handling is done in the mutation's onError callback
            logger.error({
                message: 'Failed to send reminder:',
                additionalInfo: {
                    error,
                    questionnaireId,
                },
            });
        }
    };
}

export const sharedVendorsSecurityReviewFilesMutationController =
    new VendorsSecurityReviewFilesMutationController();
