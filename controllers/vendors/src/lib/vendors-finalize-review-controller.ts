import { uniqueId } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import { vendorsSecurityReviewsControllerUpdateVendorSecurityReviewStatusMutation } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import type { VendorSecurityReviewDecision } from './types/vendor-security-review-decision.type';
import { sharedVendorsCurrentSecurityReviewsController } from './vendors-current-security-reviews-controller';
import { sharedVendorsSecurityReviewDetailsController } from './vendors-security-review-details-controller';

class VendorsFinalizeReviewController {
    constructor() {
        makeAutoObservable(this);
    }

    updateSecurityReviewStatusMutation = new ObservedMutation(
        vendorsSecurityReviewsControllerUpdateVendorSecurityReviewStatusMutation,
    );

    get isFinalizing(): boolean {
        return this.updateSecurityReviewStatusMutation.isPending;
    }

    get hasFinalizeError(): boolean {
        return this.updateSecurityReviewStatusMutation.hasError;
    }

    finalizeReview(
        securityReviewId: number,
        decision: VendorSecurityReviewDecision,
        note?: string,
        onSuccess?: () => void,
    ): void {
        if (this.isFinalizing) {
            return;
        }

        if (!securityReviewId) {
            logger.error('Security review ID is required to finalize review');

            return;
        }

        this.updateSecurityReviewStatusMutation.mutate({
            path: { id: securityReviewId },
            body: {
                status: 'COMPLETED',
                decision,
                note: note || '',
            },
        });

        when(
            () => !this.isFinalizing,
            () => {
                if (this.hasFinalizeError) {
                    snackbarController.addSnackbar({
                        id: uniqueId('finalize-review-error'),
                        props: {
                            title: t`Failed to finalize review`,
                            description: t`There was an error finalizing the security review. Please try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    logger.error({
                        message: 'Failed to finalize security review',
                        additionalInfo: {
                            securityReviewId,
                            decision,
                            note,
                            error: this.updateSecurityReviewStatusMutation
                                .error,
                        },
                    });

                    return;
                }

                sharedVendorsSecurityReviewDetailsController.securityReviewDetailsQuery.invalidate();
                sharedVendorsCurrentSecurityReviewsController.paginatedSecurityReviews.invalidate();

                snackbarController.addSnackbar({
                    id: uniqueId('finalize-review-success'),
                    hasTimeout: true,
                    props: {
                        title: t`Review finalized`,
                        description: t`The security review has been successfully finalized.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                onSuccess?.();
            },
        );
    }
}

export const sharedVendorsFinalizeReviewController =
    new VendorsFinalizeReviewController();
