import type { RowDataProps } from '@cosmos-lab/components/simple-table';
import type { VendorSecurityReviewDocumentResponseDto } from '@globals/api-sdk/types';

export interface DocumentFiles extends RowDataProps {
    name: string;
    value: string;
    documentId?: number;
    securityReviewDocumentId?: number;
}
export interface QuestionnaireFiles extends RowDataProps {
    name: string;
    customData: {
        summaryMessage?: string;
        isCompleted?: boolean;
        isManualUpload?: boolean;
        dateSent?: string;
        completedAt?: string;
        reminderDate?: string;
        responseId?: number;
        questionnaireId?: number;
        documentId?: number;
        securityReviewDocumentId?: number;
    };
}

export interface Document {
    type: VendorSecurityReviewDocumentResponseDto['type'];
    data: QuestionnaireFiles | DocumentFiles | null;
}
