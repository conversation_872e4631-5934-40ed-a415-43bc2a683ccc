import { isEmpty, isNil } from 'lodash-es';
import type { VendorSecurityReviewDocumentResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import type {
    Document,
    DocumentFiles,
    QuestionnaireFiles,
} from '../types/vendor-security-review.type';

const extractSummaryStatus = (
    summaryStatus: VendorSecurityReviewDocumentResponseDto['summaryStatus'],
    type: VendorSecurityReviewDocumentResponseDto['type'],
): string => {
    const { status, data: numberOfExceptions } = summaryStatus ?? {};

    if (status === 'SUCCESS') {
        let message =
            type === 'QUESTIONNAIRE'
                ? t`No callout found`
                : t`No exceptions found`;

        if (!isNil(numberOfExceptions) && numberOfExceptions > 0) {
            message =
                type === 'QUESTIONNAIRE'
                    ? t`Review ${numberOfExceptions} callouts found`
                    : t`Review ${numberOfExceptions} exceptions found`;
        }

        return message;
    }

    return '';
};

export const transformDocumentsToQuestionnaire = (
    data: VendorSecurityReviewDocumentResponseDto,
): QuestionnaireFiles => {
    const {
        id,
        type,
        summaryStatus,
        questionnaire,
        questionnaireReminder,
        documentId,
    } = data;

    const summaryMessage = extractSummaryStatus(summaryStatus, type);

    return {
        id: `row-${id}`,
        name: data.name,
        customData: {
            summaryMessage,
            isCompleted: questionnaire?.isCompleted,
            isManualUpload: questionnaire?.isManualUpload,
            dateSent: questionnaire?.dateSent,
            completedAt: questionnaire?.completedAt ?? undefined,
            reminderDate: questionnaireReminder?.createdAt,
            responseId: questionnaire?.responseId ?? undefined,
            questionnaireId: questionnaire?.id ?? undefined,
            documentId,
            securityReviewDocumentId: id,
        },
    };
};

export const transformDocumentsToQuestionnaires = (
    data: VendorSecurityReviewDocumentResponseDto[],
): QuestionnaireFiles[] => {
    return data.map((item) => {
        return transformDocumentsToQuestionnaire(item);
    });
};

export const transformDocumentsToFile = (
    data: VendorSecurityReviewDocumentResponseDto,
): DocumentFiles => {
    const { summaryStatus, id, name, type, documentId } = data;

    const summaryMessage = extractSummaryStatus(summaryStatus, type);

    return {
        id: `row-${id}`,
        name,
        value: summaryMessage,
        documentId,
        securityReviewDocumentId: id,
    };
};

export const transformDocumentsToFiles = (
    data: VendorSecurityReviewDocumentResponseDto[],
): DocumentFiles[] => {
    return data.map((item) => {
        return transformDocumentsToFile(item);
    });
};

export const transformToDocuments = (
    data: VendorSecurityReviewDocumentResponseDto[],
): Document[] => {
    return data.map((item) => {
        switch (item.type) {
            case 'DOCUMENT': {
                return {
                    type: item.type,
                    data: transformDocumentsToFile(item),
                };
            }
            case 'QUESTIONNAIRE': {
                return {
                    type: item.type,
                    data: transformDocumentsToQuestionnaire(item),
                };
            }
            default: {
                return {
                    type: item.type,
                    data: null,
                };
            }
        }
    });
};

/**
 * Finds the most relevant questionnaire from a list of documents.
 * Prioritizes questionnaires that have been sent (have dateSent),
 * otherwise returns the first questionnaire found.
 */
export const findRelevantQuestionnaire = (
    documents: VendorSecurityReviewDocumentResponseDto[],
): VendorSecurityReviewDocumentResponseDto | undefined => {
    const questionnaires = documents.filter(
        (doc) => doc.type === 'QUESTIONNAIRE',
    );

    if (isEmpty(questionnaires)) {
        return undefined;
    }

    // Look for a questionnaire that has been sent (has dateSent)
    const sentQuestionnaire = questionnaires.find(
        (doc) => doc.questionnaire?.dateSent,
    );

    // Return sent questionnaire if found, otherwise return the first one
    return sentQuestionnaire ?? questionnaires[0];
};
