import { describe, expect, test } from 'vitest';
import type { VendorSecurityReviewDocumentResponseDto } from '@globals/api-sdk/types';
import {
    transformDocumentsToFiles,
    transformDocumentsToQuestionnaires,
} from './vendors-security-review-documents-adapter.helper';

const createQuestionnaireMock = (
    overrides: Partial<
        VendorSecurityReviewDocumentResponseDto['questionnaire']
    > = {},
): NonNullable<VendorSecurityReviewDocumentResponseDto['questionnaire']> => ({
    id: 1,
    completedBy: null,
    recipientEmail: '<EMAIL>',
    isCompleted: true,
    dateSent: '2023-01-01',
    isManualUpload: false,
    responseId: null,
    title: 'Test Questionnaire',
    completedAt: '2023-01-02',
    totalQuestions: null,
    totalQuestionsAnswered: null,
    ...overrides,
});

const createQuestionnaireReminderMock = (
    overrides: Partial<
        VendorSecurityReviewDocumentResponseDto['questionnaireReminder']
    > = {},
): NonNullable<
    VendorSecurityReviewDocumentResponseDto['questionnaireReminder']
> => ({
    id: 1,
    result: 'Reminder sent successfully',
    scheduleConfigurationId: 1,
    createdAt: '2023-01-03',
    updatedAt: '2023-01-03',
    ...overrides,
});

const createSummaryStatusMock = (
    overrides: Partial<
        VendorSecurityReviewDocumentResponseDto['summaryStatus']
    > = {},
): NonNullable<VendorSecurityReviewDocumentResponseDto['summaryStatus']> => ({
    data: 5,
    status: 'SUCCESS',
    ...overrides,
});

const createDocumentMock = (
    overrides: Partial<VendorSecurityReviewDocumentResponseDto> = {},
): VendorSecurityReviewDocumentResponseDto => ({
    id: 123,
    documentId: 456,
    type: 'QUESTIONNAIRE',
    name: 'Test Document',
    vendorDocument: null,
    questionnaire: null,
    vendorReview: null,
    questionnaireReminder: null,
    summaryStatus: null,
    ...overrides,
});

describe('vendor Security Review Documents Adapter Helper', () => {
    describe('transformDocumentsToQuestionnaires', () => {
        test('should transform document response to questionnaire files', () => {
            const mockData: VendorSecurityReviewDocumentResponseDto[] = [
                createDocumentMock({
                    id: 123,
                    name: 'Test Questionnaire',
                    type: 'QUESTIONNAIRE',
                    questionnaire: createQuestionnaireMock({
                        isCompleted: true,
                        isManualUpload: false,
                        dateSent: '2023-01-01',
                        completedAt: '2023-01-02',
                        responseId: null,
                    }),
                    questionnaireReminder: createQuestionnaireReminderMock({
                        createdAt: '2023-01-03',
                    }),
                    summaryStatus: null,
                }),
            ];

            const result = transformDocumentsToQuestionnaires(mockData);

            expect(result).toStrictEqual([
                {
                    id: 'row-123',
                    name: 'Test Questionnaire',
                    customData: {
                        summaryMessage: '',
                        isCompleted: true,
                        isManualUpload: false,
                        dateSent: '2023-01-01',
                        completedAt: '2023-01-02',
                        reminderDate: '2023-01-03',
                        responseId: undefined,
                        questionnaireId: 1,
                        documentId: 456,
                        securityReviewDocumentId: 123,
                    },
                },
            ]);
        });

        test('should handle empty questionnaire data', () => {
            const mockData: VendorSecurityReviewDocumentResponseDto[] = [
                createDocumentMock({
                    id: 123,
                    name: 'Test Questionnaire',
                    type: 'QUESTIONNAIRE',
                }),
            ];

            const result = transformDocumentsToQuestionnaires(mockData);

            expect(result).toStrictEqual([
                {
                    id: 'row-123',
                    name: 'Test Questionnaire',
                    customData: {
                        summaryMessage: '',
                        isCompleted: undefined,
                        isManualUpload: undefined,
                        dateSent: undefined,
                        completedAt: undefined,
                        reminderDate: undefined,
                        responseId: undefined,
                        questionnaireId: undefined,
                        documentId: 456,
                        securityReviewDocumentId: 123,
                    },
                },
            ]);
        });

        test('should include responseId when available', () => {
            const mockData: VendorSecurityReviewDocumentResponseDto[] = [
                createDocumentMock({
                    id: 123,
                    name: 'Test Questionnaire',
                    type: 'QUESTIONNAIRE',
                    questionnaire: createQuestionnaireMock({
                        isCompleted: true,
                        isManualUpload: false,
                        responseId: 456,
                    }),
                }),
            ];

            const result = transformDocumentsToQuestionnaires(mockData);

            expect(result[0].customData.responseId).toBe(456);
        });
    });

    describe('transformDocumentsToFiles', () => {
        test('should transform document response with exceptions to files', () => {
            const mockData: VendorSecurityReviewDocumentResponseDto[] = [
                createDocumentMock({
                    id: 123,
                    name: 'Test File',
                    type: 'DOCUMENT',
                    summaryStatus: createSummaryStatusMock({
                        status: 'SUCCESS',
                        data: 5,
                    }),
                }),
            ];

            const result = transformDocumentsToFiles(mockData);

            expect(result).toStrictEqual([
                {
                    id: 'row-123',
                    name: 'Test File',
                    value: 'Review 5 exceptions found',
                    documentId: 456,
                    securityReviewDocumentId: 123,
                },
            ]);
        });

        test('should transform document response with no exceptions to files', () => {
            const mockData: VendorSecurityReviewDocumentResponseDto[] = [
                createDocumentMock({
                    id: 123,
                    name: 'Test File',
                    type: 'DOCUMENT',
                    summaryStatus: createSummaryStatusMock({
                        status: 'SUCCESS',
                        data: 0,
                    }),
                }),
            ];

            const result = transformDocumentsToFiles(mockData);

            expect(result).toStrictEqual([
                {
                    id: 'row-123',
                    name: 'Test File',
                    value: 'No exceptions found',
                    documentId: 456,
                    securityReviewDocumentId: 123,
                },
            ]);
        });

        test('should handle document response without summary status', () => {
            const mockData: VendorSecurityReviewDocumentResponseDto[] = [
                createDocumentMock({
                    id: 123,
                    name: 'Test File',
                    type: 'DOCUMENT',
                }),
            ];

            const result = transformDocumentsToFiles(mockData);

            expect(result).toStrictEqual([
                {
                    id: 'row-123',
                    name: 'Test File',
                    value: '',
                    documentId: 456,
                    securityReviewDocumentId: 123,
                },
            ]);
        });

        test('should handle document response with non-SUCCESS status', () => {
            const mockData: VendorSecurityReviewDocumentResponseDto[] = [
                createDocumentMock({
                    id: 123,
                    name: 'Test File',
                    type: 'DOCUMENT',
                    summaryStatus: createSummaryStatusMock({
                        status: 'ERROR',
                        data: 5,
                    }),
                }),
            ];

            const result = transformDocumentsToFiles(mockData);

            expect(result).toStrictEqual([
                {
                    id: 'row-123',
                    name: 'Test File',
                    value: '',
                    documentId: 456,
                    securityReviewDocumentId: 123,
                },
            ]);
        });

        test('should include documentId when available', () => {
            const mockData: VendorSecurityReviewDocumentResponseDto[] = [
                createDocumentMock({
                    id: 123,
                    name: 'Test File',
                    type: 'DOCUMENT',
                    documentId: 789,
                }),
            ];

            const result = transformDocumentsToFiles(mockData);

            expect(result[0].documentId).toBe(789);
            expect(result[0].securityReviewDocumentId).toBe(123);
        });
    });
});
