import { isNil, noop } from 'lodash-es';
import {
    questionnairesControllerGetQuestionnaireSummaryOptions,
    summariesControllerSaveSummarySocMutation,
} from '@globals/api-sdk/queries';
import { sharedCurrentCompanyController } from '@globals/current-company';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    reaction,
    runInAction,
    when,
} from '@globals/mobx';
import type {
    LoadingPhase,
    SocketCallbackData,
} from '../types/ai-summary-socket.types';
import {
    SKELETON_LOADING_TIME,
    SUMMARY_DEFAULT_TIME,
} from './constants/summary-ai.constants';
import {
    canRetryAISummaryError,
    getAISummaryErrorMessage,
    getAISummaryErrorTitle,
} from './helpers/ai-summary-error-messages.helper';
import { formatSummary } from './helpers/vendors-profile-questionnaire-ai-summary.helper';
import type { VendorQuestionnaireAISummary } from './types/vendor-questionnaire-ai-summary.type';
import { sharedVendorsAISummarySocketController } from './vendors-ai-summary-socket-controller';
import { sharedVendorsSecurityReviewDocumentsController } from './vendors-security-review-documents-controller';

export class VendorsProfileQuestionnaireAISummaryController {
    currentQuestionnaireId: number | null = null;
    currentSocDocumentId: number | null = null;
    loadingPhase: LoadingPhase = 'IDLE';
    summaryType: 'questionnaire' | 'soc' | null = null;

    constructor() {
        makeAutoObservable(this);

        this.initializeSocketSubscriptions();
        this.setupLoadingPhaseReactions();
    }

    setupLoadingPhaseReactions(): void {
        reaction(
            () => ({
                isLoading: this.questionnaireAISummaryQuery.isLoading,
                hasData: Boolean(
                    this.questionnaireAISummaryQuery.data?.summary,
                ),
                hasError: Boolean(
                    this.questionnaireAISummaryQuery.data?.summary?.errorCode,
                ),
                summaryType: this.summaryType,
            }),
            ({ isLoading, hasData, hasError, summaryType }) => {
                if (
                    !isLoading &&
                    (hasData || hasError) &&
                    summaryType === 'questionnaire' &&
                    this.loadingPhase === 'PROCESSING'
                ) {
                    this.loadingPhase = 'DONE';
                } else if (isLoading) {
                    this.loadingPhase = 'PROCESSING';
                }
            },
        );

        reaction(
            () => ({
                isPending: this.saveSocSummaryMutation.isPending,
                hasData: Boolean(this.saveSocSummaryMutation.response?.summary),
                hasError: Boolean(
                    this.saveSocSummaryMutation.response?.summary?.errorCode,
                ),
                summaryType: this.summaryType,
            }),
            ({ isPending, hasData, hasError, summaryType }) => {
                if (
                    !isPending &&
                    (hasData || hasError) &&
                    summaryType === 'soc' &&
                    this.loadingPhase === 'PROCESSING'
                ) {
                    this.loadingPhase = 'DONE';
                } else if (isPending) {
                    this.loadingPhase = 'PROCESSING';
                }
            },
        );
    }

    questionnaireAISummaryQuery = new ObservedQuery(
        questionnairesControllerGetQuestionnaireSummaryOptions,
    );

    saveSocSummaryMutation = new ObservedMutation(
        summariesControllerSaveSummarySocMutation,
    );

    get isLoading(): boolean {
        return this.questionnaireAISummaryQuery.isLoading;
    }

    get isSaving(): boolean {
        return this.saveSocSummaryMutation.isPending;
    }

    /**
     * Enhanced loading state computed properties.
     */
    get isRequesting(): boolean {
        return (
            this.loadingPhase === 'REQUESTING' || this.loadingPhase === 'IDLE'
        );
    }

    get isProcessing(): boolean {
        return this.loadingPhase === 'PROCESSING';
    }

    get shouldShowSkeleton(): boolean {
        return this.isProcessing && !this.summary && !this.summaryError;
    }

    get shouldShowSpinner(): boolean {
        return this.isRequesting && !this.summary && !this.summaryError;
    }

    get summarySelectedType(): 'questionnaire' | 'soc' | null {
        if (!this.summaryType) {
            return null;
        }

        return this.summaryType;
    }

    get summaryError(): string | null {
        const questionnaireError =
            this.questionnaireAISummaryQuery.data?.summary?.errorCode;
        const socError =
            this.saveSocSummaryMutation.response?.summary?.errorCode;

        const errorCode =
            this.summaryType === 'questionnaire'
                ? questionnaireError
                : socError;

        if (!errorCode) {
            return null;
        }

        return getAISummaryErrorMessage(errorCode);
    }

    get summaryErrorTitle(): string | null {
        const hasError = this.summaryError !== null;

        if (!hasError || !this.summaryType) {
            return null;
        }

        return getAISummaryErrorTitle(this.summaryType);
    }

    get canRetryError(): boolean {
        const questionnaireError =
            this.questionnaireAISummaryQuery.data?.summary?.errorCode;
        const socError =
            this.saveSocSummaryMutation.response?.summary?.errorCode;

        const errorCode =
            this.summaryType === 'questionnaire'
                ? questionnaireError
                : socError;

        if (!errorCode) {
            return false;
        }

        return canRetryAISummaryError(errorCode);
    }

    get summary(): VendorQuestionnaireAISummary | null {
        if (!sharedFeatureAccessModel.isVendorAISummaryEnabled) {
            return null;
        }

        if (this.summaryType === 'questionnaire') {
            return this.questionnaireAISummaryQuery.data?.summary &&
                !this.questionnaireAISummaryQuery.data.summary.errorCode
                ? formatSummary(
                      this.questionnaireAISummaryQuery.data.summary.summary,
                  )
                : null;
        }

        return this.saveSocSummaryMutation.response?.summary &&
            !this.saveSocSummaryMutation.hasError &&
            !this.saveSocSummaryMutation.response.summary.errorCode
            ? formatSummary(
                  this.saveSocSummaryMutation.response.summary.summary,
              )
            : null;
    }

    get executionId(): string | null {
        if (this.summaryType === 'questionnaire') {
            return (
                this.questionnaireAISummaryQuery.data?.summary?.executionId ||
                null
            );
        }

        return (
            this.saveSocSummaryMutation.response?.summary?.executionId || null
        );
    }

    get questionnaireId(): number | null {
        if (this.summaryType === 'questionnaire') {
            return this.currentQuestionnaireId;
        }

        return null;
    }

    loadQuestionnaireSummary = (questionnaireId: number): void => {
        when(
            () =>
                !sharedFeatureAccessModel.isLoading &&
                !sharedCurrentCompanyController.isLoading,
            () => {
                if (!sharedFeatureAccessModel.isVendorAISummaryEnabled) {
                    return;
                }

                this.cleanup();
                this.summaryType = 'questionnaire';
                this.currentQuestionnaireId = questionnaireId;

                this.loadingPhase = 'REQUESTING';

                this.scheduleProcessingTransition().catch(noop);

                when(
                    () => this.loadingPhase === 'PROCESSING',
                    () => {
                        this.questionnaireAISummaryQuery.load({
                            path: { id: Number(questionnaireId) },
                        });
                    },
                );
            },
        );
    };

    saveSocSummary = (retry?: boolean): void => {
        when(
            () =>
                !sharedFeatureAccessModel.isLoading &&
                !sharedCurrentCompanyController.isLoading,
            () => {
                if (!sharedFeatureAccessModel.isVendorAISummaryEnabled) {
                    return;
                }

                this.cleanup();
                this.summaryType = 'soc';
                this.loadingPhase = 'REQUESTING';
                this.scheduleProcessingTransition().catch(noop);

                when(
                    () =>
                        !sharedVendorsSecurityReviewDocumentsController.isLoading &&
                        this.loadingPhase === 'PROCESSING',
                    () => {
                        const { socDocument } =
                            sharedVendorsSecurityReviewDocumentsController;

                        if (!socDocument?.documentId) {
                            return;
                        }

                        this.saveSocSummaryMutation.mutate({
                            path: { id: socDocument.documentId },
                            body: { retry },
                        });
                    },
                );
            },
        );
    };

    retrySummary = (): void => {
        // Check access control before proceeding
        if (!sharedFeatureAccessModel.isVendorAISummaryEnabled) {
            return;
        }

        if (this.summaryType !== 'soc') {
            return;
        }
        // For questionnaire, we would need to re-upload the file. Retry only applies to SOC
        const shouldRetry = true;

        this.saveSocSummary(shouldRetry);
    };

    /**
     * Socket integration for real-time AI summary completion.
     */
    initializeSocketSubscriptions(): void {
        sharedVendorsAISummarySocketController.init();

        sharedVendorsAISummarySocketController.onQuestionnaireSummaryCompleted(
            this.handleQuestionnaireSummaryCompleted,
        );

        sharedVendorsAISummarySocketController.onSocSummaryGenerated(
            this.handleSocSummaryGenerated,
        );
    }

    /**
     * Schedule transition from REQUESTING to PROCESSING phase.
     */
    async scheduleProcessingTransition(
        timeout = SUMMARY_DEFAULT_TIME,
    ): Promise<void> {
        // Use Promise-based delay for UI updates (LEGACY of web)
        await new Promise<void>((resolve) => {
            setTimeout(() => {
                resolve();
            }, timeout);
        });

        runInAction(() => {
            if (this.loadingPhase === 'REQUESTING') {
                this.loadingPhase = 'PROCESSING';

                return;
            }

            if (this.loadingPhase === 'PROCESSING') {
                this.loadingPhase = 'DONE';
            }
        });
    }

    handleQuestionnaireSummaryCompleted = (data: SocketCallbackData): void => {
        if (!sharedFeatureAccessModel.isVendorAISummaryEnabled) {
            return;
        }

        if (!data.vendorDocument) {
            return;
        }

        const { id: questionnaireId } = data.vendorDocument;

        if (
            Number(questionnaireId) !== this.currentQuestionnaireId ||
            this.summaryType !== 'questionnaire'
        ) {
            return;
        }

        this.scheduleProcessingTransition(SKELETON_LOADING_TIME).catch(noop);
        this.loadingPhase = 'DONE';

        this.questionnaireAISummaryQuery.invalidate();
    };

    handleSocSummaryGenerated = (data: SocketCallbackData): void => {
        if (!sharedFeatureAccessModel.isVendorAISummaryEnabled) {
            return;
        }

        if (!data.vendorDocument) {
            return;
        }

        const { id: documentId } = data.vendorDocument;
        const { socDocument } = sharedVendorsSecurityReviewDocumentsController;

        if (
            this.summaryType !== 'soc' ||
            !documentId ||
            Number(documentId) !== socDocument?.documentId
        ) {
            return;
        }

        this.scheduleProcessingTransition(SKELETON_LOADING_TIME).catch(noop);

        if (
            this.saveSocSummaryMutation.isPending ||
            !isNil(this.summary) ||
            !isNil(this.summaryError)
        ) {
            return;
        }

        this.saveSocSummaryMutation.mutate({
            path: { id: socDocument.documentId },
            body: { retry: false },
        });
    };

    cleanup(): void {
        this.currentQuestionnaireId = null;
        this.currentSocDocumentId = null;
        this.loadingPhase = 'IDLE';
        this.summaryType = null;
        this.questionnaireAISummaryQuery.unload();
    }
}

export const sharedVendorsProfileQuestionnaireAISummaryController =
    new VendorsProfileQuestionnaireAISummaryController();
