import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import type { Action } from '@cosmos/components/action-stack';
import type { ColorScheme } from '@cosmos/components/text';
import { makeAutoObservable } from '@globals/mobx';
import { CHECK_STATUS_ENUM } from '@views/monitoring';
import {
    createHorizontalMenuItems,
    createPageHeaderItems,
} from '../helpers/monitoring-details-actions.helpers';

interface ActionConfiguration {
    headerActions: Action[];
    horizontalMenuOptions: {
        id: string;
        label: string;
        colorScheme?: ColorScheme;
        onSelect: () => void;
    }[];
}

interface ActionParams {
    isTestDisabledOrUnused: boolean;
    horizontalMenuItems: ReturnType<typeof createHorizontalMenuItems>;
    pageHeaderItems: ReturnType<typeof createPageHeaderItems>;
}

interface CustomTestActionParams extends ActionParams {
    draft: boolean;
}

export class MonitoringDetailsActionsController {
    constructor() {
        makeAutoObservable(this);
    }

    getActionConfiguration(): ActionConfiguration {
        const { testDetails, source } = sharedMonitoringTestDetailsController;

        if (!testDetails) {
            return { headerActions: [], horizontalMenuOptions: [] };
        }

        const horizontalMenuItems = createHorizontalMenuItems();
        const pageHeaderItems = createPageHeaderItems();
        const { checkStatus, draft } = testDetails;
        const isTestDisabledOrUnused =
            checkStatus === CHECK_STATUS_ENUM.UNUSED ||
            checkStatus === CHECK_STATUS_ENUM.DISABLED;

        if (source === 'CUSTOM') {
            return this.getCustomTestActions({
                draft,
                isTestDisabledOrUnused,
                horizontalMenuItems,
                pageHeaderItems,
            });
        }

        return this.getDefaultTestActions({
            isTestDisabledOrUnused,
            horizontalMenuItems,
            pageHeaderItems,
        });
    }

    private getCustomTestActions({
        draft,
        isTestDisabledOrUnused,
        horizontalMenuItems,
        pageHeaderItems,
    }: CustomTestActionParams): ActionConfiguration {
        if (draft) {
            return isTestDisabledOrUnused
                ? {
                      headerActions: [pageHeaderItems.publishDraft],
                      horizontalMenuOptions: [horizontalMenuItems.deleteDraft],
                  }
                : {
                      headerActions: [pageHeaderItems.testNow],
                      horizontalMenuOptions: [
                          horizontalMenuItems.publishDraft,
                          horizontalMenuItems.deleteDraft,
                      ],
                  };
        }

        // Published custom test
        return isTestDisabledOrUnused
            ? {
                  headerActions: [pageHeaderItems.goToEvidence],
                  horizontalMenuOptions: [horizontalMenuItems.deleteTest],
              }
            : {
                  headerActions: [pageHeaderItems.testNow],
                  horizontalMenuOptions: [
                      horizontalMenuItems.goToEvidence,
                      horizontalMenuItems.deleteTest,
                  ],
              };
    }

    private getDefaultTestActions({
        isTestDisabledOrUnused,
        horizontalMenuItems,
        pageHeaderItems,
    }: ActionParams): ActionConfiguration {
        return isTestDisabledOrUnused
            ? {
                  headerActions: [pageHeaderItems.goToEvidence],
                  horizontalMenuOptions: [],
              }
            : {
                  headerActions: [pageHeaderItems.testNow],
                  horizontalMenuOptions: [horizontalMenuItems.goToEvidence],
              };
    }
}

export const sharedMonitoringDetailsActionsController =
    new MonitoringDetailsActionsController();
