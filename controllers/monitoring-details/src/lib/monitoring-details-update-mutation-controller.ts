import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { snackbarController } from '@controllers/snackbar';
import { monitorsControllerPutMonitorDetailsMutation } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { activeMonitoringDetailsController } from './monitoring-details-controller';

class MonitoringDetailsUpdateMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    updateMutation = new ObservedMutation(
        monitorsControllerPutMonitorDetailsMutation,
        {
            onSuccess: () => {
                activeMonitoringDetailsController.monitorDetails.invalidate();
                sharedMonitoringTestDetailsController.testDetailsQuery.invalidate();

                snackbarController.addSnackbar({
                    id: 'monitor-details-updated',
                    props: {
                        title: t`Test details updated successfully`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'monitor-details-update-error',
                    props: {
                        title: t`Failed to update test details`,
                        description: t`An error occurred while updating the test details. Please try again.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    get isUpdating(): boolean {
        return this.updateMutation.isPending;
    }

    get hasError(): boolean {
        return this.updateMutation.hasError;
    }

    updateMonitorDetails = (
        testId: number,
        details: { name: string; description: string | undefined },
        onSuccess?: () => void,
    ): void => {
        const { currentWorkspace, isLoading } = sharedWorkspacesController;

        when(
            () => !isLoading,
            () => {
                if (!currentWorkspace) {
                    throw new Error('Workspace not found');
                }

                this.updateMutation.mutate({
                    path: {
                        testId,
                        xProductId: currentWorkspace.id,
                    },
                    body: {
                        name: details.name,
                        description: details.description,
                    },
                });

                when(
                    () => !this.updateMutation.isPending,
                    () => {
                        if (!this.updateMutation.hasError) {
                            onSuccess?.();
                        }
                    },
                );
            },
        );
    };
}

export const sharedMonitoringDetailsUpdateMutationController =
    new MonitoringDetailsUpdateMutationController();
