import type { Action } from '@cosmos/components/action-stack';
import type { ColorScheme } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { MONITORING_DETAILS_ACTION_IDS } from '../constants/monitoring-details-actions.constants';

export const createHorizontalMenuItems = (): Record<
    string,
    {
        id: string;
        label: string;
        colorScheme?: ColorScheme;
        onSelect: () => void;
    }
> => ({
    publishDraft: {
        id: MONITORING_DETAILS_ACTION_IDS.PUBLISH_DRAFT_OPTION,
        label: t`Publish draft`,
        onSelect: () => {
            // TODO: Implement publish draft logic
        },
    },
    deleteDraft: {
        id: MONITORING_DETAILS_ACTION_IDS.DELETE_DRAFT_OPTION,
        label: t`Delete draft`,
        colorScheme: 'critical' as ColorScheme,
        onSelect: () => {
            // TODO: Implement delete draft logic
        },
    },
    deleteTest: {
        id: MONITORING_DETAILS_ACTION_IDS.DELETE_TEST_OPTION,
        label: t`Delete test`,
        colorScheme: 'critical' as ColorScheme,
        onSelect: () => {
            // TODO: Implement delete test logic
        },
    },
    goToEvidence: {
        id: MONITORING_DETAILS_ACTION_IDS.GO_TO_EVIDENCE_OPTION,
        label: t`Go to evidence`,
        onSelect: () => {
            // TODO: Implement go to evidence logic
        },
    },
});

export const createPageHeaderItems = (): Record<string, Action> => ({
    publishDraft: {
        actionType: 'button',
        id: MONITORING_DETAILS_ACTION_IDS.PUBLISH_DRAFT_BUTTON,
        typeProps: {
            label: t`Publish draft`,
            level: 'tertiary',
            onClick: () => {
                // TODO: Implement publish draft logic
            },
        },
    },
    testNow: {
        actionType: 'button',
        id: MONITORING_DETAILS_ACTION_IDS.TEST_NOW_BUTTON,
        typeProps: {
            label: t`Test now`,
            level: 'tertiary',
            onClick: () => {
                // TODO: Implement test now logic
            },
        },
    },
    deleteTest: {
        actionType: 'button',
        id: MONITORING_DETAILS_ACTION_IDS.DELETE_TEST_BUTTON,
        typeProps: {
            label: t`Delete test`,
            level: 'secondary',
            colorScheme: 'danger',
            onClick: () => {
                // TODO: Implement delete test logic
            },
        },
    },
    goToEvidence: {
        actionType: 'button',
        id: MONITORING_DETAILS_ACTION_IDS.GO_TO_EVIDENCE_BUTTON,
        typeProps: {
            label: t`Go to evidence`,
            level: 'tertiary',
            onClick: () => {
                // TODO: Implement go to evidence logic
            },
        },
    },
});
