import type { ListBoxItemData } from '@cosmos/components/list-box';
import { controlWorkspaceGroupControllerGetLinkedWorkspacesByPolicyOptions } from '@globals/api-sdk/queries';
import type { ControlWorkspaceGroupResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

export class LinkedWorkspacesByPolicyController {
    constructor() {
        makeAutoObservable(this);
    }

    linkedWorkspacesByPolicyQuery = new ObservedQuery(
        controlWorkspaceGroupControllerGetLinkedWorkspacesByPolicyOptions,
    );

    load = (policyId: number, controlGroupId: number): void => {
        this.linkedWorkspacesByPolicyQuery.load({
            path: {
                policyId,
                controlGroupId,
            },
        });
    };

    get policyLinkedWorkspacesGroup(): ControlWorkspaceGroupResponseDto | null {
        return this.linkedWorkspacesByPolicyQuery.data;
    }

    get policyLinkedWorkspaces(): ControlWorkspaceGroupResponseDto['linkedWorkspaces'] {
        return this.linkedWorkspacesByPolicyQuery.data?.linkedWorkspaces ?? [];
    }

    get policyLinkedWorkspacesOptions(): ListBoxItemData[] {
        return this.policyLinkedWorkspaces.map((ws) => ({
            id: String(ws.workspace.id),
            label: ws.workspace.name,
            value: String(ws.workspace.id),
        }));
    }

    get isLoading(): boolean {
        return this.linkedWorkspacesByPolicyQuery.isLoading;
    }
}

export const sharedLinkedWorkspacesByPolicyController =
    new LinkedWorkspacesByPolicyController();
