import type { ListBoxItemData } from '@cosmos/components/list-box';
import { controlWorkspaceGroupControllerGetLinkedWorkspacesByEvidenceOptions } from '@globals/api-sdk/queries';
import type { ControlWorkspaceGroupResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery } from '@globals/mobx';

export class LinkedWorkspacesByEvidenceController {
    constructor() {
        makeAutoObservable(this);
    }

    linkedWorkspacesByEvidenceQuery = new ObservedQuery(
        controlWorkspaceGroupControllerGetLinkedWorkspacesByEvidenceOptions,
    );

    load = (evidenceId: number, controlGroupId: number): void => {
        this.linkedWorkspacesByEvidenceQuery.load({
            path: {
                evidenceId,
                controlGroupId,
            },
        });
    };

    get controlLinkedWorkspacesGroup(): ControlWorkspaceGroupResponseDto | null {
        return this.linkedWorkspacesByEvidenceQuery.data;
    }

    get evidenceLinkedWorkspaces(): ControlWorkspaceGroupResponseDto['linkedWorkspaces'] {
        return (
            this.linkedWorkspacesByEvidenceQuery.data?.linkedWorkspaces ?? []
        );
    }

    get evidenceLinkedWorkspacesOptions(): ListBoxItemData[] {
        return this.evidenceLinkedWorkspaces.map((ws) => ({
            id: String(ws.workspace.id),
            label: ws.workspace.name,
            value: String(ws.workspace.id),
        }));
    }

    get isLoading(): boolean {
        return this.linkedWorkspacesByEvidenceQuery.isLoading;
    }
}

export const sharedLinkedWorkspacesByEvidenceController =
    new LinkedWorkspacesByEvidenceController();
