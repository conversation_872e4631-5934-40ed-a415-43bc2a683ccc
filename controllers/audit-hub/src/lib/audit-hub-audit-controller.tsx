import { isEmpty, isNumber } from 'lodash-es';
import { sharedAuditHubAuditorClientAuditController } from '@controllers/audit-hub-auditor-client-audit';
import { sharedAuditorController } from '@controllers/auditor';
import { sharedCustomerRequestsController } from '@controllers/customer-requests';
import { snackbarController } from '@controllers/snackbar';
import { Button } from '@cosmos/components/button';
import {
    auditorControllerGenerateControlEvidencePackageMutation,
    companiesControllerGetAllOptions,
    customerRequestControllerCreateCustomerRequestsFromRequirementsMutation,
    customerRequestControllerCreateCustomerRequestsMutation,
    customerRequestControllerGetCustomerRequestFieldValidationOptions,
    customerRequestControllerGetRequestsFileTemplateOptions,
    customerRequestControllerValidateCsvFileMutation,
} from '@globals/api-sdk/queries';
import type {
    CreateCustomerRequestRequestDto,
    CustomerRequestFileValidationResponseDto,
    RequestGetFieldValidationResponseDto,
} from '@globals/api-sdk/types';
import { sharedCurrentUserController } from '@globals/current-user';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    runInAction,
    toJS,
    when,
} from '@globals/mobx';
import { downloadFileFromSignedUrl } from '@helpers/download-file';
import { getFileIssueErrorMessage } from '../../../frameworks/src/constants/csv-upload-messages.const';
import { sharedAuditHubController } from './audit-hub-controller';
import { ERROR_IDS, ERROR_MESSAGES } from './constants/errors.constants';
import { formatControlEvidenceRequest } from './helpers/control-evidence.helper';
import type { SampleWizardData } from './types/wizard.data.types';

class AuditHubAuditController {
    wizardData: Partial<SampleWizardData> = {};
    auditorFrameworkId: string | null = null;
    clientId: string | null = null;
    _lastValidatedField: string | null = null;
    /**
     * Indicates whether pre-audit package is ready.
     * Set to false when evidence generation starts, set to true when socket event confirms completion.
     * Used to control UI loading states and button availability.
     */
    isPreAuditPackageReady = true;

    /**
     * Bulk upload state.
     */
    uploadedFiles: File[] = [];
    fileValidationError: string | null = null;
    fileUploadKey = 0;

    getRequestFileTemplate = new ObservedQuery(
        customerRequestControllerGetRequestsFileTemplateOptions,
    );

    createCustomerRequests = new ObservedMutation(
        customerRequestControllerCreateCustomerRequestsMutation,
    );

    generateControlEvidencePackage = new ObservedMutation(
        auditorControllerGenerateControlEvidencePackageMutation,
    );

    validateCsv = new ObservedMutation(
        customerRequestControllerValidateCsvFileMutation,
    );

    getAllCompanies = new ObservedQuery(companiesControllerGetAllOptions);

    createCustomerRequestsFromRequirements = new ObservedMutation(
        customerRequestControllerCreateCustomerRequestsFromRequirementsMutation,
    );

    customerRequestFieldValidationQuery = new ObservedQuery(
        customerRequestControllerGetCustomerRequestFieldValidationOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    addSampleWizardData = (data: Partial<SampleWizardData>) => {
        this.wizardData = {
            ...this.wizardData,
            ...data,
        };
    };

    loadRequestTemplate = (): void => {
        this.getRequestFileTemplate.load();
    };

    startAudit = async (): Promise<void> => {
        try {
            const { auditByIdData } = sharedAuditHubController;
            const shouldGenerateRequirementsFromFile =
                this.wizardData.evidenceOption === 'use-custom';

            if (shouldGenerateRequirementsFromFile) {
                await this._generateControlEvidenceFromFile();
            } else {
                await this._generateControlEvidencesFromRequirements();
            }

            this.loadAllCompanies(Number(auditByIdData?.framework.productId));
        } catch (error) {
            logger.error('error-start-audit - Failed to start audit');
            this._handleError(
                ERROR_IDS.AUDIT_START,
                ERROR_MESSAGES.AUDIT_START_FAILED,
                ERROR_MESSAGES.AUDIT_START_DESCRIPTION,
            );
            throw error;
        }
    };

    get requestTemplate(): string | null {
        return this.getRequestFileTemplate.data;
    }

    get sampleWizardData(): Partial<SampleWizardData> {
        return toJS(this.wizardData);
    }

    get csvValidationResponse() {
        return this.validateCsv.response;
    }

    get getAllCompaniesIsLoading(): boolean {
        return this.getAllCompanies.isLoading;
    }

    get isValidatingCsv(): boolean {
        return this.validateCsv.isPending;
    }

    get isCreatingCustomerRequests(): boolean {
        return this.createCustomerRequests.isPending;
    }

    get isDownloadingTemplate(): boolean {
        return this.getRequestFileTemplate.isLoading;
    }

    get isCsvValid(): boolean {
        return (
            Boolean(this.validateCsv.response) &&
            this.validateCsv.response?.status === 'VALID'
        );
    }

    get requestFieldValidationResult(): RequestGetFieldValidationResponseDto | null {
        return this.customerRequestFieldValidationQuery.data ?? null;
    }

    get isRequestFieldValidationLoading(): boolean {
        return this.customerRequestFieldValidationQuery.isLoading;
    }

    get isRequestFieldValid(): boolean {
        return (
            this.customerRequestFieldValidationQuery.data?.isFieldAvailable ??
            false
        );
    }

    _handleError = (id: string, title: string, description: string): void => {
        snackbarController.addSnackbar({
            id,
            props: {
                title,
                description,
                severity: 'critical',
                closeButtonAriaLabel: 'Close',
            },
        });
    };

    _validateCsvFile = async (
        file: File,
    ): Promise<CustomerRequestFileValidationResponseDto | undefined> => {
        if (!this.auditorFrameworkId) {
            throw new Error(
                'Auditor framework ID is required for CSV validation',
            );
        }

        await this.validateCsv.mutateAsync({
            body: {
                file,
                auditorFrameworkId: this.auditorFrameworkId,
            },
        });

        const validationResponse = this.validateCsv.response;

        if (validationResponse?.status === 'VALID') {
            return validationResponse;
        }

        // Get error message from the first issue if available
        let errorDescription = ERROR_MESSAGES.CSV_VALIDATION_DESCRIPTION;

        if (validationResponse?.issues[0]?.type) {
            errorDescription = getFileIssueErrorMessage(
                validationResponse.issues[0].type as Parameters<
                    typeof getFileIssueErrorMessage
                >[0],
            );
        }

        this._handleError(
            ERROR_IDS.CSV_VALIDATION,
            ERROR_MESSAGES.CSV_VALIDATION_FAILED,
            errorDescription,
        );

        return undefined;
    };

    _generateControlEvidenceFromFile = async (): Promise<void> => {
        try {
            const file =
                this.wizardData.customEvidenceRequest?.evidenceRequestList?.[0];

            if (!file) {
                throw new Error(ERROR_MESSAGES.NO_FILE_PROVIDED);
            }

            const validationResponse = await this._validateCsvFile(file);

            this.addSampleWizardData({
                customEvidenceRequest: {
                    requests: validationResponse?.requests,
                },
            });

            await this.createCustomerRequests.mutateAsync({
                body: {
                    isBulkUpload: false,
                    isWizardUpload: true,
                    requests: (this.wizardData.customEvidenceRequest
                        ?.requests ?? []) as CreateCustomerRequestRequestDto[],
                },
            });

            await this._generateControlEvidence();
        } catch (error) {
            logger.error(
                'error-on-generating-control-evidence-from-file - Failed to generate control evidence from file',
            );

            this._handleError(
                ERROR_IDS.CONTROL_EVIDENCE,
                ERROR_MESSAGES.CONTROL_EVIDENCE_FAILED,
                ERROR_MESSAGES.CONTROL_EVIDENCE_DESCRIPTION,
            );
            throw error;
        }
    };

    _generateControlEvidencesFromRequirements = async (): Promise<void> => {
        if (!this.auditorFrameworkId) {
            throw new Error('Auditor framework ID is required');
        }

        await this._generateControlEvidence();
        await this.createCustomerRequestsFromRequirements.mutateAsync({
            body: {
                auditorFrameworkId: this.auditorFrameworkId,
            },
        });
    };

    _generateControlEvidence = async (): Promise<void> => {
        const { auditByIdData } = sharedAuditHubController;
        const { entryId } = sharedCurrentUserController;

        if (!auditByIdData) {
            throw new Error(ERROR_MESSAGES.AUDIT_DATA_UNAVAILABLE);
        }

        const request = formatControlEvidenceRequest(
            this.sampleWizardData,
            auditByIdData,
        );

        await this.generateControlEvidencePackage.mutateAsync({
            path: {
                entryId,
                xProductId: auditByIdData.framework.productId,
            },
            body: {
                ...request,
            },
        });

        // Invalidate related queries after successful control evidence generation
        this._invalidateAuditDetailsQueries();
    };

    /**
     * Invalidates queries that need to be refreshed after sample evidence changes.
     * This ensures the AuditorClientAuditDetailsView displays updated data.
     */
    _invalidateAuditDetailsQueries = (): void => {
        const { auditByIdData } = sharedAuditHubController;

        if (auditByIdData?.framework) {
            logger.info({
                message:
                    'Invalidating audit details queries after sample evidence changes',
                additionalInfo: {
                    auditId: auditByIdData.framework.id,
                    frameworkType: auditByIdData.framework.type,
                },
            });

            // Invalidate audit summary data (used in donut chart)
            sharedAuditorController.auditSummaryByIdQuery.invalidate();

            // Invalidate customer requests list (used in request table)
            sharedCustomerRequestsController.customerRequestListQuery.invalidate();

            // Invalidate auditor client audit data (used in assigned auditors table)
            sharedAuditHubAuditorClientAuditController.auditorsQuery.invalidate();
            sharedAuditHubAuditorClientAuditController.auditDatesQuery.invalidate();
        }
    };

    loadAllCompanies = (productId: number): void => {
        if (!this.auditorFrameworkId) {
            throw new Error('Auditor framework ID is required');
        }

        this.isPreAuditPackageReady = false;

        this.getAllCompanies.load({
            query: {
                auditorFrameworkId: this.auditorFrameworkId,
            },
            path: {
                xProductId: productId,
            },
        });

        when(
            () => !this.getAllCompanies.isLoading,
            () => {
                if (this.getAllCompanies.hasError) {
                    logger.error(
                        `${this.getAllCompanies.error}-get-all-companies-error`,
                    );
                    snackbarController.addSnackbar({
                        id: 'get-all-companies-error',
                        props: {
                            title: t`Couldn't create pre audit package`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                            link: (
                                <Button
                                    colorScheme="primary"
                                    label={t`Try again`}
                                    level="tertiary"
                                    size="sm"
                                    onClick={() => {
                                        runInAction(() => {
                                            if (!isNumber(productId)) {
                                                return;
                                            }
                                            // Invalidate and reload the query instead of calling loadAllCompanies
                                            this.getAllCompanies.invalidate();
                                            this.loadAllCompanies(productId);
                                        });
                                    }}
                                />
                            ),
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: 'refresh-control-evidence-package-success',
                    props: {
                        title: t`Auditors will be notified via email when your download is ready.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };

    downloadTemplate = (): void => {
        this.loadRequestTemplate();

        when(() => !this.getRequestFileTemplate.isLoading)
            .then(() => {
                const templateData = this.requestTemplate;

                if (templateData) {
                    downloadFileFromSignedUrl(templateData);

                    snackbarController.addSnackbar({
                        id: 'template-download-success',
                        props: {
                            title: t`The template CSV is being downloaded`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    logger.error('Unable to download template csv file.');
                    snackbarController.addSnackbar({
                        id: 'template-download-error',
                        props: {
                            title: t`Unable to download template csv file.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                }
            })
            .catch(() => {
                logger.error('Unable to download template csv file.');
                snackbarController.addSnackbar({
                    id: 'template-download-error',
                    props: {
                        title: t`Unable to download template csv file.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            });
    };

    validateBulkCsv = async (file: File): Promise<boolean> => {
        const validationResponse = await this._validateCsvFile(file);

        return validationResponse?.status === 'VALID';
    };

    createBulkCustomerRequests = async (): Promise<boolean> => {
        try {
            const validationResponse = this.validateCsv.response;

            if (!validationResponse?.requests) {
                logger.error('bulk-create-error-No validated requests found');

                snackbarController.addSnackbar({
                    id: 'bulk-create-error',
                    props: {
                        title: t`No validated requests found`,
                        description: t`Please upload and validate a CSV file first.`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                return false;
            }

            await this.createCustomerRequests.mutateAsync({
                body: {
                    isBulkUpload: true,
                    isWizardUpload: false,
                    requests:
                        validationResponse.requests as CreateCustomerRequestRequestDto[],
                },
            });

            snackbarController.addSnackbar({
                id: 'bulk-create-success',
                props: {
                    title: t`Customer requests created successfully`,
                    severity: 'success',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            this.refreshCustomerRequestsTable();

            return true;
        } catch {
            logger.error(
                'bulk-create-error-Failed to create customer requests',
            );

            snackbarController.addSnackbar({
                id: 'bulk-create-error',
                props: {
                    title: t`Failed to create customer requests`,
                    description: t`An error occurred while creating the requests. Please try again.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return false;
        }
    };

    createSingleCustomerRequest = async (requestData: {
        requestId: string;
        title: string;
        description?: string;
        mappedControls?: string[];
    }): Promise<boolean> => {
        if (!this.auditorFrameworkId) {
            snackbarController.addSnackbar({
                id: 'single-create-error',
                props: {
                    title: t`Auditor framework ID is required`,
                    description: t`Please ensure the audit is properly configured.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return false;
        }

        try {
            const request: CreateCustomerRequestRequestDto = {
                code: requestData.requestId,
                title: requestData.title,
                description: requestData.description || '',
                controlIds: requestData.mappedControls?.map(Number) ?? null,
                auditorFrameworkId: this.auditorFrameworkId,
            };

            await this.createCustomerRequests.mutateAsync({
                body: {
                    isBulkUpload: false,
                    isWizardUpload: false,
                    requests: [request],
                },
            });

            snackbarController.addSnackbar({
                id: 'single-create-success',
                props: {
                    title: t`Customer request created successfully`,
                    severity: 'success',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            this.refreshCustomerRequestsTable();

            return true;
        } catch {
            logger.error(
                'single-create-error-Failed to create customer request',
            );
            snackbarController.addSnackbar({
                id: 'single-create-error',
                props: {
                    title: t`Failed to create customer request`,
                    description: t`An error occurred while creating the request. Please try again.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return false;
        }
    };

    refreshCustomerRequestsTable = (): void => {
        sharedCustomerRequestsController.customerRequestListQuery.invalidate();
    };

    validateRequestField = (field: string): void => {
        if (!field.trim()) {
            this._lastValidatedField = null;

            return;
        }

        // Skip validation if the field hasn't changed since last validation
        if (this._lastValidatedField && this._lastValidatedField === field) {
            return;
        }

        if (!this.auditorFrameworkId) {
            throw new Error(
                'Auditor framework ID is required for field validation',
            );
        }

        this._lastValidatedField = field;
        this.customerRequestFieldValidationQuery.load({
            path: { auditorFrameworkId: this.auditorFrameworkId },
            query: { code: field.trim() },
        });
    };

    /**
     * Bulk upload methods.
     */
    handleFileUpdate = async (files: { file: File }[]): Promise<void> => {
        runInAction(() => {
            this.uploadedFiles = files.map((f) => f.file);
            this.fileValidationError = null;
        });

        if (isEmpty(files)) {
            return;
        }

        if (files[0]) {
            const { file } = files[0];
            const isValid = await this.validateBulkCsv(file);

            if (!isValid) {
                runInAction(() => {
                    this.uploadedFiles = [];
                    this.fileUploadKey = this.fileUploadKey + 1;
                });
            }
        }
    };

    handleBulkSubmit = async (): Promise<boolean> => {
        if (isEmpty(this.uploadedFiles)) {
            runInAction(() => {
                this.fileValidationError = t`Please upload a CSV file before saving.`;
            });

            return false;
        }

        const success = await this.createBulkCustomerRequests();

        if (success) {
            runInAction(() => {
                this.uploadedFiles = [];
                this.fileValidationError = null;
                this.fileUploadKey = this.fileUploadKey + 1;
            });
        }

        return success;
    };

    resetBulkUploadState = (): void => {
        runInAction(() => {
            this.uploadedFiles = [];
            this.fileValidationError = null;
            this.fileUploadKey = this.fileUploadKey + 1;
        });
    };
}

export const sharedAuditHubAuditController = new AuditHubAuditController();
