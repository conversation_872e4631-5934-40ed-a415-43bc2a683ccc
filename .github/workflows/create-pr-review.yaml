name: Create - PR Review

on:
  pull_request:
    types: [labeled]

permissions:
  contents: read
  pull-requests: write

jobs:
  review-pr:
    name: PR Review
    runs-on: ubuntu-latest
    if: github.event.label.name == 'PR Review'
    steps:
      - name: Generate PR Review
        uses: augmentcode/review-pr@v0
        with:
          augment_session_auth:
            '{"accessToken":"${{ secrets.AUGMENT_ACCESS_TOKEN
            }}","tenantURL":"https://e1.api.augmentcode.com/","scopes":["read","write"]}'
          github_token: ${{ secrets.GITHUB_TOKEN }}
          pull_number: ${{ github.event.pull_request.number }}
          repo_name: ${{ github.repository }}
          bot_name: "<PERSON><PERSON><PERSON>"
          bot_avatar_url: "https://raw.githubusercontent.com/drata/multiverse/main/.github/assets/bot-avatar.png"
          custom_guidelines: |
            ## MANDATORY CODE QUALITY ENFORCEMENT

            STRICTLY ENFORCE ALL GUIDELINES - NO EXCEPTIONS

            ### PRIMARY REFERENCE DOCUMENTS
            You MUST follow the complete review process defined in:
            - `.llm/critical-rules/ai-code-review-checklist.md` - COMPLETE REVIEW PROCESS (FOLLOW EVERY STEP)
            - `.llm/critical-rules/ai-code-quality-guidelines.md` - CORE OPERATING PRINCIPLES (APPLY FIRST)
            - `.llm/PATTERN_INDEX.md` - Project-specific conventions (CHECK SECOND)

            ### ENFORCEMENT REQUIREMENTS
            - [ ] Apply EVERY checklist item from ai-code-review-checklist.md
            - [ ] Follow the STRICT ORDER defined in the enforcement protocol
            - [ ] Use the MANDATORY FEEDBACK FORMAT for all violations
            - [ ] Apply ALL approval criteria before approving any code
            - [ ] Reference specific .llm/ documentation in all feedback

            NO EXCEPTIONS - ALL GUIDELINES MUST BE FOLLOWED
