import { isEmpty } from 'lodash-es';
import type {
    IObjectSelectorController,
    ObjectItem,
    ObjectSelectorConfig,
} from '@components/object-selector';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { DateTime } from '@cosmos-lab/components/date-time';
import { policiesControllerListExternalDocumentsInfiniteOptions } from '@globals/api-sdk/queries';
import type {
    ExternalFileTypeResponseDto,
    PoliciesControllerListExternalDocumentsData,
} from '@globals/api-sdk/types';
import { t, Trans } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedInfiniteQuery } from '@globals/mobx';
import { DOC } from '@helpers/evidence';

class PoliciesExternalFilesController
    implements IObjectSelectorController<ExternalFileTypeResponseDto>
{
    _selectedItems: ObjectItem<ExternalFileTypeResponseDto>[] = [];
    _searchTerm = '';
    _config: ObjectSelectorConfig | null = null;

    externalFilesInfiniteQuery = new ObservedInfiniteQuery(
        policiesControllerListExternalDocumentsInfiniteOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    get availableItems(): ObjectItem<ExternalFileTypeResponseDto>[] {
        const files = this.externalFiles;

        return files.map((file) => this.mapToObjectItem(file));
    }

    get selectedItems(): ObjectItem<ExternalFileTypeResponseDto>[] {
        return this._selectedItems;
    }

    get searchTerm(): string {
        return this._searchTerm;
    }

    get isLoading(): boolean {
        return this.externalFilesInfiniteQuery.isLoading;
    }

    get hasError(): boolean {
        return Boolean(this.externalFilesInfiniteQuery.error);
    }

    get errorMessage(): string | undefined {
        return this.externalFilesInfiniteQuery.error?.message;
    }

    get hasNextPage(): boolean {
        return this.externalFilesInfiniteQuery.hasNextPage;
    }

    get selectedCountText(): string {
        const count = this._selectedItems.length;

        if (count === 0) {
            return t`No files selected`;
        }

        if (count === 1) {
            return t`1 file selected`;
        }

        return t`${count} files selected`;
    }

    get modalTitle(): string {
        return this._config?.modal.title || t`Select External Files`;
    }

    get isConfirmDisabled(): boolean {
        // Disable confirm if selection mode is single/multi and no items selected based on config
        return Boolean(
            this._config?.selectionMode && isEmpty(this._selectedItems),
        );
    }

    get externalFiles(): ExternalFileTypeResponseDto[] {
        return (
            this.externalFilesInfiniteQuery.data?.pages.flatMap(
                (page) => page?.data ?? [],
            ) ?? []
        );
    }

    get hasMore(): boolean {
        return this.externalFilesInfiniteQuery.hasNextPage;
    }

    get total(): number {
        const firstPage = this.externalFilesInfiniteQuery.data?.pages[0];

        return firstPage?.total ?? 0;
    }

    initialize(config: ObjectSelectorConfig): void {
        this._config = config;

        this.loadItems();
    }

    loadItems(): void {
        this.externalFilesInfiniteQuery.load({
            query: {
                nameSearch: this._searchTerm || '',
            },
        });
    }

    loadNextPage = (): void => {
        if (this.hasNextPage && !this.isLoading) {
            this.externalFilesInfiniteQuery.nextPage();
        }
    };

    search(term: string): void {
        this._searchTerm = term;
        this.externalFilesInfiniteQuery.invalidate();
        this.loadItems();
    }

    setSelectedItems(items: ObjectItem<ExternalFileTypeResponseDto>[]): void {
        this._selectedItems = items;
    }

    addSelectedItems(items: ObjectItem<ExternalFileTypeResponseDto>[]): void {
        const newItemIds = new Set(items.map((item) => item.id));
        const existingItems = this._selectedItems.filter(
            (item) => !newItemIds.has(item.id),
        );

        this._selectedItems = [...existingItems, ...items];
    }

    removeSelectedItem(id: string): void {
        this._selectedItems = this._selectedItems.filter(
            (item) => item.id !== id,
        );
    }

    clearSelectedItems(): void {
        this._selectedItems = [];
    }

    reset(): void {
        this._selectedItems = [];
        this._searchTerm = '';
        this._config = null;
        this.externalFilesInfiniteQuery.unload();
    }

    private mapToObjectItem(
        file: ExternalFileTypeResponseDto,
    ): ObjectItem<ExternalFileTypeResponseDto> {
        // This is reused logic from api
        const fileExtension = file.originalName?.split('.');
        const isFileExtensionValid = fileExtension
            ? DOC.extensions.includes(
                  `.${fileExtension[fileExtension.length - 1]}`,
              )
            : true;

        const endSlot = (
            <Stack direction="column" gap="sm" align="end">
                <Text>
                    <Trans>Created on: </Trans>
                    <DateTime date={file.createdAt} format="table" />
                </Text>
                {!isFileExtensionValid && (
                    <Metadata
                        colorScheme="warning"
                        label={t`Invalid file type`}
                        type="status"
                        iconName="WarningDiamond"
                    />
                )}
            </Stack>
        );

        return {
            id: file.id,
            value: file.id,
            label: file.name,
            description: `${file.path}/${file.originalName}`,
            objectType: 'EXTERNAL_POLICY_FILE',
            objectData: file,
            primaryText: file.name,
            disabled: !isFileExtensionValid,
            endSlot,
            secondaryText: `${file.path}/${file.originalName}`,
            metadata: {
                createdAt: file.createdAt,
                isFileExtensionValid,
            },
        };
    }

    loadExternalFiles = (
        params: PoliciesControllerListExternalDocumentsData['query'],
    ): void => {
        this.externalFilesInfiniteQuery.load({
            query: params,
        });
    };
}

export const sharedPoliciesExternalFilesController =
    new PoliciesExternalFilesController();
