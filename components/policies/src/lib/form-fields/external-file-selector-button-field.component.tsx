import { isEmpty, noop } from 'lodash-es';
import { useState } from 'react';
import type { ObjectItem } from '@components/object-selector';
import { openPolicyExternalFileSelector } from '@components/policies';
import { sharedPoliciesController } from '@controllers/policies';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Feedback } from '@cosmos/components/feedback';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Tooltip } from '@cosmos/components/tooltip';
import { DateTime } from '@cosmos-lab/components/date-time';
import type { ExternalFileTypeResponseDto } from '@globals/api-sdk/types';
import { t, Trans } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import type { CustomFieldRenderProps } from '@ui/forms';

/**
 * Custom field component that replaces the policies dropdown with a button
 * that opens the global policy selector modal.
 */
export const ExternalPolicyFileSelectionField = observer(
    (props: CustomFieldRenderProps): React.JSX.Element => {
        const {
            value = [],
            setValue,
            onBlur,
            error,
            'data-id': dataId,
        } = props;
        const [selectedFile, setSelectedFile] =
            useState<ObjectItem<ExternalFileTypeResponseDto> | null>(null);

        const { externalPolicyProviderName } = sharedPoliciesController;

        const handleSubmit = action(
            (
                selectedItems:
                    | ObjectItem<ExternalFileTypeResponseDto>[]
                    | ObjectItem<ExternalFileTypeResponseDto>,
            ) => {
                // Handle both single and multi-select responses
                const file = Array.isArray(selectedItems)
                    ? selectedItems[0]
                    : selectedItems;

                setSelectedFile(file);

                setValue(file.id);
            },
        );

        const handleOpenModal = action(() => {
            openPolicyExternalFileSelector({
                config: {
                    selectionMode: 'single',
                    modal: {
                        id: 'policies-external-file-selector',
                        title: t`Browse file`,
                        size: 'lg',
                        confirmButtonLabel: t`Import`,
                        cancelButtonLabel: t`Cancel`,
                        showSelectedCount: true,
                    },
                    search: {
                        placeholder: t`Search by file name...`,
                        label: t`Search file`,
                        loaderLabel: t`Loading files...`,
                        emptyStateMessage: t`No files found matching your search criteria.`,
                        clearAllLabel: t`Clear all`,
                    },
                    filters: {
                        // Exclude already selected policies if needed
                        excludeIds: Array.isArray(value)
                            ? value.map(String)
                            : [],
                    },
                },
                callbacks: {
                    onSelected: handleSubmit,
                    onCancel: noop,
                },
            });
        });

        const getButtonLabel = () => {
            if (!isEmpty(value)) {
                return t`Replace selected file`;
            }

            return t`Browse file`;
        };

        const handleResetSelections = action(() => {
            setValue([]);
        });

        return (
            <Stack
                direction="column"
                gap="sm"
                data-testid="ExternalPolicyFileSelectionField"
                data-id={dataId}
            >
                <Text
                    type="body"
                    size="200"
                    colorScheme="neutral"
                    data-id={`${dataId}-help-text`}
                >
                    {t`Select one file from ${externalPolicyProviderName} you would like to import. Drata supports the following file extensions: .pdf, .docx, .odt, .xlsx, .ods, .pptx, .odp`}
                </Text>
                <Button
                    label={getButtonLabel()}
                    level="secondary"
                    size="md"
                    data-id={`${dataId}-button`}
                    onClick={handleOpenModal}
                    onBlur={onBlur}
                />

                {!isEmpty(value) && (
                    <Stack
                        direction="row"
                        align="center"
                        justify="between"
                        width="100%"
                    >
                        <Stack direction="row" align="center" gap="3x">
                            <Box width="dimension40x">
                                <Text
                                    size="200"
                                    type="body"
                                    colorScheme="neutral"
                                >
                                    {selectedFile?.label}
                                </Text>
                            </Box>
                            <Text type="body" size="100" colorScheme="faded">
                                <Trans>Created on </Trans>
                                <DateTime
                                    format="table"
                                    date={
                                        selectedFile?.objectData
                                            .createdAt as string
                                    }
                                    textProps={{
                                        type: 'body',
                                        size: '100',
                                        colorScheme: 'faded',
                                    }}
                                />
                            </Text>
                        </Stack>
                        <Stack direction="row" align="center" gap="2x">
                            <Tooltip isInteractive text={t`Remove file`}>
                                <Button
                                    isIconOnly
                                    startIconName="Trash"
                                    label={t`Remove file`}
                                    type="button"
                                    level="tertiary"
                                    colorScheme="neutral"
                                    size="sm"
                                    data-id="9h-PCR1Y"
                                    onClick={handleResetSelections}
                                />
                            </Tooltip>
                        </Stack>
                    </Stack>
                )}

                {error?.message && (
                    <Feedback
                        title={error.message}
                        severity="critical"
                        data-id={`${dataId}-error`}
                    />
                )}
            </Stack>
        );
    },
);
