import { useMemo, useState } from 'react';
import { z } from 'zod';
import { sharedEditReviewPeriodController } from '@controllers/access-reviews';
import type { TDateISODate } from '@cosmos/components/date-picker-field';
import { Feedback } from '@cosmos/components/feedback';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { dimension170x } from '@cosmos/constants/tokens';
import { t, Trans } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { Form, type FormSchema, type FormValues } from '@ui/forms';

export const PeriodsStep = observer(
    ({
        formRef,
        formId,
        onSubmit,
    }: {
        formRef: React.ForwardedRef<HTMLFormElement>;
        formId: string;
        onSubmit: () => void;
    }): React.JSX.Element => {
        const [hasError, setHasError] = useState(false);
        const { formData, setPeriodDetailsData } =
            sharedEditReviewPeriodController;

        const formSchema: FormSchema = useMemo(() => {
            return {
                dateRange: {
                    type: 'dateRange',
                    label: t`Review period`,
                    initialValue: {
                        start: formData.startingDate as TDateISODate,
                        end: formData.endingDate as TDateISODate,
                    },
                    locale: 'en-US' as const,
                    validator: z
                        .object({
                            start: z
                                .string({
                                    message: t`Start date is required`,
                                })
                                .date(t`Please select a valid start date`),
                            end: z
                                .string({
                                    message: t`End date is required`,
                                })
                                .date(t`Please select a valid end date`),
                        })
                        .refine(
                            (data) => {
                                if (!data.start || !data.end) {
                                    return true;
                                } // Let individual field validation handle this
                                setHasError(true);

                                return (
                                    new Date(data.start) < new Date(data.end)
                                );
                            },
                            {
                                message: t`End date must be after start date`,
                                path: ['end'],
                            },
                        ),
                },
            };
        }, [formData]);

        const handleSubmit = (values: FormValues) => {
            const dateRange = values.dateRange as {
                start: TDateISODate | null;
                end: TDateISODate | null;
            };

            if (dateRange.start && dateRange.end) {
                action(() => {
                    setPeriodDetailsData({
                        startingDate: dateRange.start as string,
                        endingDate: dateRange.end as string,
                    });
                })();

                onSubmit();
            }
        };

        return (
            <Stack
                gap="xl"
                direction="column"
                data-testid="PeriodsStep"
                data-id="access-review-period-details-step"
                style={{ maxWidth: dimension170x }}
            >
                <Stack gap="lg" direction="column">
                    <Text type="subheadline" size="400">
                        <Trans>Define your review period</Trans>
                    </Text>
                    <Text type="body" size="200">
                        <Trans>
                            Select the start and end dates for your company to
                            perform the next access review.
                        </Trans>
                    </Text>
                </Stack>

                <Form
                    hasExternalSubmitButton
                    formId={formId}
                    data-id={formId}
                    ref={formRef}
                    schema={formSchema}
                    key={`period-details-create`}
                    onSubmit={handleSubmit}
                />
                {hasError && (
                    <Feedback
                        severity="critical"
                        title={t`End date must be after start date`}
                    />
                )}
            </Stack>
        );
    },
);
