import {
    closeMonitorUpdateCodebaseExclusionReasonModal,
    openMonitorUpdateCodebaseExclusionReasonModal,
} from '@components/monitor-update-exclusion-reason-modal';
import { sharedMonitoringCodeExclusionsController } from '@controllers/monitoring';
import { sharedCodebaseFindingExclusionMutationController } from '@controllers/monitoring-details';
import { SchemaDropdown } from '@cosmos/components/schema-dropdown';
import type { MonitorCodebaseExclusionResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const MonitoringCodebaseActionsCell = observer(
    ({
        row: { original },
    }: {
        row: { original: MonitorCodebaseExclusionResponseDto };
    }): React.JSX.Element => {
        const { testId } = sharedMonitoringCodeExclusionsController;

        return (
            <SchemaDropdown
                isIconOnly
                size="sm"
                startIconName="HorizontalMenu"
                level="tertiary"
                label={t`Horizontal menu`}
                colorScheme="neutral"
                data-id="6GvBWQ_R"
                data-testid="MonitoringCodebaseActionsCell"
                items={[
                    {
                        id: 'code-update-reason',
                        label: t`Update reason`,
                        type: 'item',
                        value: 'code-update-reason',
                        onClick: () => {
                            openMonitorUpdateCodebaseExclusionReasonModal({
                                exclusion: original,
                                onConfirm: (reason: string) => {
                                    if (!testId) {
                                        return;
                                    }

                                    sharedCodebaseFindingExclusionMutationController.updateExclusionReason(
                                        original.id,
                                        testId,
                                        reason,
                                        () => {
                                            closeMonitorUpdateCodebaseExclusionReasonModal();
                                        },
                                    );
                                },
                            });
                        },
                    },
                    {
                        id: 'code-include-remove-exclusion',
                        label: t`Include/remove exclusion`,
                        type: 'item',
                        value: 'code-include-remove-exclusion',
                        colorScheme: 'critical',
                    },
                ]}
            />
        );
    },
);
