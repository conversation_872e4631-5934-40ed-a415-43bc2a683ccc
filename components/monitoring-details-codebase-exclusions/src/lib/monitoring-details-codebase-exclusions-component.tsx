import { useCallback } from 'react';
import { AppDatatable } from '@components/app-datatable';
import { sharedMonitoringCodeExclusionsController } from '@controllers/monitoring';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedMonitoringDetailsCodebaseExclusionsTableModel } from './models/monitoring-details-codebase-exclusions-table.model';

export const MonitoringDetailsCodebaseExclusionsComponent = observer(
    (): React.JSX.Element => {
        const {
            isLoading,
            monitoringCodeExclusionsData,
            monitoringCodeExclusionsTotal,
            monitoringCodeExclusionLoad,
        } = sharedMonitoringCodeExclusionsController;

        const {
            columns,
            datatableRef,
            bulkActions,
            handleRowSelection,
            emptyStateProps,
        } = sharedMonitoringDetailsCodebaseExclusionsTableModel;

        const preventSelectAllAcrossPages = useCallback(() => true, []);

        return (
            <AppDatatable
                isRowSelectionEnabled={preventSelectAllAcrossPages}
                data-testid="MonitoringCodebaseExclusionsComponent"
                isLoading={isLoading}
                tableId="datatable-monitoring-codebase-exclusions"
                data-id="datatable-monitoring-codebase-exclusions"
                data={monitoringCodeExclusionsData}
                total={monitoringCodeExclusionsTotal}
                getRowId={(row) => row.id.toString()}
                columns={columns}
                imperativeHandleRef={datatableRef}
                bulkActionDropdownItems={bulkActions}
                emptyStateProps={emptyStateProps}
                filterViewModeProps={{
                    props: {
                        selectedOption: 'pinned',
                        initialSelectedOption: 'pinned',
                        togglePinnedLabel: t`Pin filters to page`,
                        toggleUnpinnedLabel: t`Move filters to dropdown`,
                    },
                    viewMode: 'toggleable',
                }}
                onFetchData={monitoringCodeExclusionLoad}
                onRowSelection={handleRowSelection}
            />
        );
    },
);
