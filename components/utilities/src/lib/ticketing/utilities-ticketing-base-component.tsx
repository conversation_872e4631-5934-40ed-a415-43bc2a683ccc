import type React from 'react';
import { ActionStack } from '@cosmos/components/action-stack';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import {
    dimension2xl,
    dimension4xl,
    dimension6xl,
    dimensionXl,
} from '@cosmos/constants/tokens';
import type { TicketResponseDto } from '@globals/api-sdk/types';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { UtilitiesTicketingEmptyStateComponent } from './utilities-ticketing-empty-state-component';
import { UtilitiesTicketingTicketCardComponent } from './utilities-ticketing-ticket-card-component';

export interface UtilitiesTicketingBaseComponentProps {
    objectName: string;
    ticketsInProgress: TicketResponseDto[];
    isLoadingTicketsInProgress: boolean;
    isLoadingTicketsCompleted: boolean;
    totalTicketsInProgress: number;
    totalTicketsCompleted: number;
    userHasPermissionToCreateTicket: boolean;
    hasNextPage: boolean;
    loadNextPage: () => void;
    deleteTicket: (ticketId: number) => void;
    isDeletingTicket: boolean;
    onCreateTicket: () => void;
    onViewClosedTickets?: () => void;
    'data-id'?: string;
}

export const UtilitiesTicketingBaseComponent = observer(
    ({
        objectName,
        ticketsInProgress,
        isLoadingTicketsInProgress,
        isLoadingTicketsCompleted,
        totalTicketsInProgress,
        totalTicketsCompleted,
        userHasPermissionToCreateTicket,
        hasNextPage,
        loadNextPage,
        deleteTicket,
        isDeletingTicket,
        onCreateTicket,
        onViewClosedTickets,
        'data-id': dataId,
    }: UtilitiesTicketingBaseComponentProps): React.JSX.Element => {
        return (
            <Stack
                direction="column"
                height="100%"
                width="100%"
                data-testid="UtilitiesNotesComponent"
                data-id={dataId || '9329QCnO'}
                py="xl"
                pl="xl"
                pr="2xl"
                overflowY="auto"
            >
                <Stack
                    gap="2x"
                    direction="row"
                    pb="lg"
                    align="start"
                    justify="start"
                >
                    <Stack
                        direction="column"
                        gap="sm"
                        align="start"
                        justify="start"
                    >
                        <Text size="300" type="title">
                            <Trans>Tickets</Trans>
                        </Text>
                        {isLoadingTicketsInProgress ? (
                            <Skeleton
                                width="100%"
                                barHeight={dimensionXl}
                                barCount={1}
                                data-id="header-subtitle-skeleton"
                            />
                        ) : (
                            <Text size="100">
                                <Trans>
                                    Showing {totalTicketsInProgress} tickets in
                                    progress associated with {objectName}
                                </Trans>
                            </Text>
                        )}

                        {totalTicketsCompleted > 0 && (
                            <Button
                                label={t`View closed tickets`}
                                level="tertiary"
                                colorScheme="primary"
                                onClick={onViewClosedTickets}
                            />
                        )}
                    </Stack>
                    {userHasPermissionToCreateTicket &&
                        totalTicketsInProgress !== 0 &&
                        !isLoadingTicketsInProgress && (
                            <Box flexShrink={'0'} flexGrow={'1'}>
                                <Button
                                    label={t`Create ticket`}
                                    level="secondary"
                                    colorScheme="primary"
                                    onClick={onCreateTicket}
                                />
                            </Box>
                        )}
                </Stack>
                <Stack width="100%" direction="column">
                    <Stack gap="lg" direction="column">
                        {/* no tickets in progress */}
                        {totalTicketsInProgress === 0 &&
                            !isLoadingTicketsInProgress &&
                            !isLoadingTicketsCompleted && (
                                <UtilitiesTicketingEmptyStateComponent
                                    totalTicketsCompleted={
                                        totalTicketsCompleted
                                    }
                                    userHasPermissionToCreateTicket={
                                        userHasPermissionToCreateTicket
                                    }
                                    onCreateTicket={onCreateTicket}
                                    onViewClosedTickets={
                                        totalTicketsCompleted > 0
                                            ? onViewClosedTickets
                                            : undefined
                                    }
                                />
                            )}
                        {/* loading tickets */}
                        {isLoadingTicketsInProgress &&
                            Array.from({ length: 3 }).map((_, index) => (
                                <Box
                                    // eslint-disable-next-line react/no-array-index-key -- its loading its fine
                                    key={`ticket-skeleton-${index}`}
                                    p="xl"
                                    borderRadius="borderRadiusLg"
                                    borderWidth="borderWidth1"
                                    borderColor="neutralBorderFaded"
                                    backgroundColor="neutralBackgroundSurfaceInitial"
                                    data-id={`ticket-skeleton-${index}`}
                                >
                                    <Stack gap="xl" direction="column">
                                        {/* Title and metadata */}
                                        <Stack direction="column" gap="lg">
                                            <Skeleton
                                                width="70%"
                                                barHeight={dimension2xl}
                                                barCount={1}
                                                data-id={`ticket-title-skeleton-${index}`}
                                            />
                                            <Stack
                                                gap="md"
                                                direction="row"
                                                align="center"
                                            >
                                                <Skeleton
                                                    width={dimension4xl}
                                                    barHeight={dimension2xl}
                                                    barCount={1}
                                                    data-id={`ticket-tag-skeleton-${index}`}
                                                />
                                                <Skeleton
                                                    width="80px"
                                                    barHeight="16px"
                                                    barCount={1}
                                                    data-id={`ticket-number-skeleton-${index}`}
                                                />
                                            </Stack>
                                        </Stack>
                                        {/* Key-value pairs */}
                                        <Stack direction="column" gap="xl">
                                            <Stack gap="lg" direction="row">
                                                <Stack
                                                    direction="column"
                                                    gap="sm"
                                                    width="50%"
                                                >
                                                    <Skeleton
                                                        width={dimension4xl}
                                                        barHeight="14px"
                                                        barCount={1}
                                                        data-id={`ticket-status-label-skeleton-${index}`}
                                                    />
                                                    <Skeleton
                                                        width={dimension6xl}
                                                        barHeight="16px"
                                                        barCount={1}
                                                        data-id={`ticket-status-value-skeleton-${index}`}
                                                    />
                                                </Stack>
                                                <Stack
                                                    direction="column"
                                                    gap="sm"
                                                    width="50%"
                                                >
                                                    <Skeleton
                                                        width="70px"
                                                        barHeight="14px"
                                                        barCount={1}
                                                        data-id={`ticket-assignee-label-skeleton-${index}`}
                                                    />
                                                    <Skeleton
                                                        width="90px"
                                                        barHeight="16px"
                                                        barCount={1}
                                                        data-id={`ticket-assignee-value-skeleton-${index}`}
                                                    />
                                                </Stack>
                                            </Stack>
                                            <Stack direction="column" gap="lg">
                                                <Stack
                                                    direction="column"
                                                    gap="sm"
                                                >
                                                    <Skeleton
                                                        width="90px"
                                                        barHeight="14px"
                                                        barCount={1}
                                                        data-id={`ticket-updated-label-skeleton-${index}`}
                                                    />
                                                    <Skeleton
                                                        width="140px"
                                                        barHeight="16px"
                                                        barCount={1}
                                                        data-id={`ticket-updated-value-skeleton-${index}`}
                                                    />
                                                </Stack>
                                                <Stack
                                                    direction="column"
                                                    gap="sm"
                                                >
                                                    <Skeleton
                                                        width="80px"
                                                        barHeight="14px"
                                                        barCount={1}
                                                        data-id={`ticket-created-label-skeleton-${index}`}
                                                    />
                                                    <Skeleton
                                                        width="100px"
                                                        barHeight="16px"
                                                        barCount={1}
                                                        data-id={`ticket-created-value-skeleton-${index}`}
                                                    />
                                                </Stack>
                                            </Stack>
                                        </Stack>
                                        {/* Action buttons */}
                                        <Stack
                                            gap="lg"
                                            direction="row"
                                            align="center"
                                        >
                                            <Skeleton
                                                width="120px"
                                                barHeight="32px"
                                                barCount={1}
                                                data-id={`ticket-manage-button-skeleton-${index}`}
                                            />
                                            <Skeleton
                                                width="32px"
                                                barHeight="32px"
                                                barCount={1}
                                                data-id={`ticket-download-button-skeleton-${index}`}
                                            />
                                            <Skeleton
                                                width="32px"
                                                barHeight="32px"
                                                barCount={1}
                                                data-id={`ticket-delete-button-skeleton-${index}`}
                                            />
                                        </Stack>
                                    </Stack>
                                </Box>
                            ))}
                        {/* tickets */}
                        {!isLoadingTicketsInProgress &&
                            ticketsInProgress.map(
                                (ticket: TicketResponseDto) => (
                                    <UtilitiesTicketingTicketCardComponent
                                        key={ticket.id}
                                        ticket={ticket}
                                        isDeletingTicket={isDeletingTicket}
                                        data-id="9qyLt9hO"
                                        onDeleteTicket={deleteTicket}
                                    />
                                ),
                            )}
                        {hasNextPage && (
                            <ActionStack
                                gap="lg"
                                stacks={[
                                    {
                                        actions: [
                                            {
                                                actionType: 'button' as const,
                                                id: 'load-more-button',
                                                typeProps: {
                                                    label: t`Load more`,
                                                    level: 'tertiary' as const,
                                                    colorScheme:
                                                        'primary' as const,
                                                    onClick: loadNextPage,
                                                },
                                            },
                                        ],
                                        id: 'load-more-stack',
                                    },
                                ]}
                            />
                        )}
                    </Stack>
                </Stack>
            </Stack>
        );
    },
);
