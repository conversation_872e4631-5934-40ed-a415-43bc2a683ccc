import { isEmpty } from 'lodash-es';
import { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import { z } from 'zod';
import { modalController } from '@controllers/modal';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import type {
    CosmosFileObject,
    SupportedFormat,
} from '@cosmos/components/file-upload';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Tooltip } from '@cosmos/components/tooltip';
import { DateTime } from '@cosmos-lab/components/date-time';
import {
    StackedList,
    StackedListItem,
} from '@cosmos-lab/components/stacked-list';
import type {
    AuditHubEvidenceResponseDto,
    NoteFileResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { downloadBlob } from '@helpers/download-file';
import {
    type FormSche<PERSON>,
    <PERSON><PERSON>rapper,
    UniversalForm<PERSON>ield,
    useFormSubmit,
} from '@ui/forms';
import { AttachmentFormModalContent } from './attachment-form-modal-content.component';
import {
    DEFAULT_NOTES_MAX_FILE_SIZE_IN_MB,
    UTILITIES_NOTES_COMMENT_ATTACHMENT_FORM_MODAL_ID,
} from './utilities-notes-comment-attachment-form-constant';
import type { ReferenceDocument } from './utilities-notes-comment-types';

const formId = 'NoteCommentForm';
const MAX_SOURCE_CHARACTERS = 768;

const getUtilitiesNotesCommentFormSchema = ({
    comment,
    maxNoteCharacters,
    source,
    hasSource,
    attachmentConfig = {},
    commentLabel = t`New note`,
    sourceLabel = t`Source`,
    documentActive,
    referenceDocuments = [],
    editMode = false,
}: {
    comment: string;
    attachments?: NoteFileResponseDto[];
    maxNoteCharacters: number;
    source: string;
    hasSource: boolean;
    attachmentConfig?: {
        showAddAttachment?: 'modal' | 'field';
        acceptedFormats?: SupportedFormat[];
        allowMultipleFiles?: boolean;
        maxFileSizeInMB?: number;
    };
    commentLabel?: string;
    sourceLabel?: string;
    documentActive?: AuditHubEvidenceResponseDto | null;
    referenceDocuments?: ReferenceDocument[];
    editMode?: boolean;
}): FormSchema => {
    const {
        allowMultipleFiles = false,
        acceptedFormats,
        showAddAttachment,
        maxFileSizeInMB = DEFAULT_NOTES_MAX_FILE_SIZE_IN_MB,
    } = attachmentConfig;

    const maxFileSizeInBytes = maxFileSizeInMB * 1024 * 1024;

    const shouldShowReferenceField =
        documentActive && (!editMode || !isEmpty(referenceDocuments));

    let documentName = '';

    if (documentActive) {
        if (editMode && !isEmpty(referenceDocuments)) {
            documentName = referenceDocuments[0].documentName;
        } else {
            documentName = documentActive.name;
        }
    }

    return {
        comment: {
            type: 'textarea',
            maxCharacters: maxNoteCharacters,
            initialValue: comment,
            label: commentLabel,
            rows: 9,
            labelStyleOverrides: {
                size: 'sm',
            },
            validator: z.string().refine(
                (value) => {
                    const trimmedValue = value.trim();

                    return !isEmpty(trimmedValue);
                },
                {
                    message: t`Comment cannot be empty`,
                },
            ),
        },
        ...(shouldShowReferenceField
            ? {
                  hasReference: {
                      type: 'checkbox',
                      label: t`Include reference link to evidence: ${documentName}`,
                      initialValue: editMode && !isEmpty(referenceDocuments),
                  },
              }
            : null),
        ...(showAddAttachment === 'field'
            ? {
                  files: {
                      maxFileSizeInBytes,
                      type: 'file',
                      label: allowMultipleFiles
                          ? t`Attachments`
                          : t`Attachment`,
                      isOptional: true,
                      initialValue: [],
                      selectButtonText: allowMultipleFiles
                          ? t`Select files`
                          : t`Select file`,
                      removeButtonText: t`Remove file`,
                      acceptedFormats: acceptedFormats ?? ['jpeg', 'png'],
                      oneFileOnly: !allowMultipleFiles,
                      isMulti: allowMultipleFiles,
                      errorCodeMessages: {
                          'file-invalid-type': t`Not a valid file type.`,
                          'file-too-large': t`File cannot be larger than ${maxFileSizeInMB}MB.`,
                          'file-too-small': t`File size is too small.`,
                          'too-many-files': t`Contains too many files.`,
                      },
                      innerLabel: allowMultipleFiles
                          ? t`Or drop files here`
                          : t`Or drop file here`,
                  },
              }
            : null),
        ...(hasSource
            ? {
                  source: {
                      type: 'textarea',
                      isOptional: true,
                      maxCharacters: MAX_SOURCE_CHARACTERS,
                      label: sourceLabel,
                      initialValue: source,
                      rows: 9,
                  },
              }
            : null),
    };
};

interface NoteAttachment {
    name: string;
    creationDate: string;
    file: CosmosFileObject;
}

export const NoteCommentForm = ({
    noteId,
    comment,
    attachments,
    maxNoteCharacters,
    source,
    hasSource,
    editMode,
    onSubmit,
    onCancel,
    onDownloadAttachment,
    commentLabel,
    sourceLabel,
    labels = {},
    attachmentConfig = {},
    isLoading = false,
    documentActive,
    referenceDocuments = [],
}: {
    noteId?: string;
    comment: string;
    attachments: NoteFileResponseDto[];
    maxNoteCharacters: number;
    source: string;
    hasSource: boolean;
    editMode: boolean;
    isLoading?: boolean;
    onDownloadAttachment?: (attachmentId: string, noteId?: string) => void;
    commentLabel?: string;
    sourceLabel?: string;
    labels?: {
        confirmationButton?: string;
    };
    attachmentConfig?: {
        showAddAttachment?: 'modal' | 'field';
        acceptedFormats?: SupportedFormat[];
        useSimpleAttachments?: boolean;
        includeAttachmentTitle?: boolean;
        includeAttachmentCreationDate?: boolean;
        allowMultipleFiles?: boolean;
        maxFileSizeInMB?: number;
    };
    onChange?: (hasChanges: boolean) => void;
    documentActive?: AuditHubEvidenceResponseDto | null;
    referenceDocuments?: ReferenceDocument[];
    onSubmit: (
        data: {
            comment: string;
            source: string;
            files: {
                name: string;
                creationDate: string;
                file: CosmosFileObject;
            }[];
            filesToDelete: string[];
            hasReference: boolean;
        },
        onSuccess?: () => void,
    ) => void;
    onCancel?: (
        getCurrentValues?: () => { comment: string; source: string },
    ) => void;
}): React.JSX.Element => {
    const schema = useMemo(
        () =>
            getUtilitiesNotesCommentFormSchema({
                comment,
                attachments,
                maxNoteCharacters,
                source,
                hasSource,
                attachmentConfig,
                commentLabel,
                sourceLabel,
                documentActive,
                referenceDocuments,
                editMode,
            }),
        [
            comment,
            attachments,
            maxNoteCharacters,
            source,
            hasSource,
            commentLabel,
            sourceLabel,
            attachmentConfig,
            documentActive,
            referenceDocuments,
            editMode,
        ],
    );

    const shouldShowReferenceField =
        documentActive && (!editMode || !isEmpty(referenceDocuments));

    const [files, setFiles] = useState<NoteAttachment[]>([]);
    const [deletedAttachmentIds, setDeletedAttachmentIds] = useState<string[]>(
        [],
    );

    // Compute existing attachments, filtering out deleted ones
    const existingAttachments = useMemo(
        () =>
            attachments.filter((att) => !deletedAttachmentIds.includes(att.id)),
        [attachments, deletedAttachmentIds],
    );

    const getDefaultActionLabel = () => {
        if (editMode) {
            return t`Save note`;
        }

        return t`Save note`;
    };

    const actionButtonLabel =
        labels.confirmationButton ?? getDefaultActionLabel();

    const handleDeleteExistingAttachment = (attachmentId: string) => {
        setDeletedAttachmentIds([...deletedAttachmentIds, attachmentId]);
    };

    const modalContent = useCallback(
        () => (
            <AttachmentFormModalContent
                data-id="nM7RrUmB"
                attachmentConfig={{
                    ...attachmentConfig,
                    includeAttachmentCreationDate: true,
                }}
                onSubmit={(newAttachments) => {
                    if (isEmpty(newAttachments)) {
                        return;
                    }

                    const newFiles: NoteAttachment[] = newAttachments.map(
                        (attachment) => ({
                            ...attachment,
                            file: {
                                file: attachment.file,
                                errors: [],
                            },
                        }),
                    );

                    const updatedFiles: NoteAttachment[] = [
                        ...files,
                        ...newFiles,
                    ];

                    setFiles(updatedFiles);
                }}
            />
        ),
        [files, attachmentConfig],
    );

    const { formRef, triggerClearForm, getFormValues } = useFormSubmit();

    const getCurrentFormValues = useCallback(() => {
        const values = getFormValues();

        if (values) {
            return {
                comment: (values.comment as string) || '',
                source: (values.source as string) || '',
            };
        }

        return { comment: '', source: '' };
    }, [getFormValues]);

    return (
        <FormWrapper
            schema={schema}
            data-testid="NoteCommentForm"
            data-id="iGWF_VL5"
            ref={formRef}
            formId={formId}
            onSubmit={({
                comment: newComment,
                source: newSource,
                files: filesFromField = [],
                hasReference,
            }) => {
                const filesFormattedFromField =
                    attachmentConfig.showAddAttachment === 'field'
                        ? (filesFromField as File[]).map((f) => ({
                              name: f.name,
                              creationDate: f.lastModified.toString(),
                              file: { file: f, errors: [] },
                          }))
                        : [];

                onSubmit(
                    {
                        comment: newComment as string,
                        source: newSource as string,
                        files:
                            attachmentConfig.showAddAttachment === 'modal'
                                ? files
                                : filesFormattedFromField,
                        filesToDelete: deletedAttachmentIds,
                        hasReference: hasReference as boolean,
                    },
                    () => {
                        triggerClearForm();
                        setFiles([]);
                        setDeletedAttachmentIds([]);
                    },
                );
            }}
        >
            <UniversalFormField
                name="comment"
                showDivider={false}
                formId={formId}
                data-id="comment-field"
                labelStyleOverrides={{
                    size: 'md',
                    type: 'title',
                }}
            />
            {shouldShowReferenceField && (
                <UniversalFormField
                    name="hasReference"
                    showDivider={false}
                    formId={formId}
                    data-id="has-reference-field"
                    labelStyleOverrides={{
                        size: 'md',
                        type: 'title',
                    }}
                />
            )}
            {hasSource && (
                <UniversalFormField
                    name="source"
                    showDivider={false}
                    formId={formId}
                    data-id="source-field"
                    labelStyleOverrides={{
                        size: 'md',
                        type: 'title',
                    }}
                />
            )}
            {attachmentConfig.showAddAttachment === 'field' && (
                <UniversalFormField
                    name="files"
                    showDivider={false}
                    formId={formId}
                    data-id="files-field"
                    labelStyleOverrides={{
                        size: 'md',
                        type: 'title',
                    }}
                />
            )}

            {editMode && !isEmpty(existingAttachments) && (
                <Box pt="3x">
                    <StackedList aria-label={t`Existing attachments`}>
                        {existingAttachments.map((attachment) => (
                            <StackedListItem
                                key={`existing-attachment-${attachment.id}`}
                                data-id={`existing-attachment-${attachment.id}`}
                                primaryColumn={
                                    <Stack
                                        direction="row"
                                        align="center"
                                        justify="between"
                                        width="100%"
                                    >
                                        <Stack
                                            direction="row"
                                            align="center"
                                            gap="3x"
                                        >
                                            <Box width="150px">
                                                <Text
                                                    size="200"
                                                    type="body"
                                                    colorScheme="neutral"
                                                >
                                                    {attachment.name}
                                                </Text>
                                            </Box>
                                            <Text
                                                type="body"
                                                size="100"
                                                colorScheme="faded"
                                            >
                                                {t`Created on`}{' '}
                                                <DateTime
                                                    date={attachment.createdAt}
                                                    format="sentence"
                                                    textProps={{
                                                        type: 'body',
                                                        size: '100',
                                                        colorScheme: 'faded',
                                                    }}
                                                />
                                            </Text>
                                        </Stack>
                                        <Stack direction="row" gap="2x">
                                            <Tooltip
                                                isInteractive
                                                text={t`Download file`}
                                            >
                                                <Button
                                                    isIconOnly
                                                    startIconName="Download"
                                                    label={t`Download file`}
                                                    type="button"
                                                    level="tertiary"
                                                    colorScheme="neutral"
                                                    size="sm"
                                                    onClick={() => {
                                                        onDownloadAttachment?.(
                                                            attachment.id,
                                                            noteId,
                                                        );
                                                    }}
                                                />
                                            </Tooltip>
                                            <Tooltip
                                                isInteractive
                                                text={t`Remove file`}
                                            >
                                                <Button
                                                    isIconOnly
                                                    startIconName="Trash"
                                                    label={t`Remove file`}
                                                    type="button"
                                                    level="tertiary"
                                                    colorScheme="danger"
                                                    size="sm"
                                                    data-id={`remove-attachment-${attachment.id}`}
                                                    onClick={() => {
                                                        handleDeleteExistingAttachment(
                                                            attachment.id,
                                                        );
                                                    }}
                                                />
                                            </Tooltip>
                                        </Stack>
                                    </Stack>
                                }
                            />
                        ))}
                    </StackedList>
                </Box>
            )}

            {/* Display newly added files */}
            {!isEmpty(files) && (
                <Box pt="3x">
                    <StackedList aria-label={t`New attachments`}>
                        {files.map((file, index) => (
                            <StackedListItem
                                key={`new-attachment-${file.name}`}
                                data-id={`new-attachment-${file.name}`}
                                primaryColumn={
                                    <Stack
                                        direction="row"
                                        align="center"
                                        justify="between"
                                        width="100%"
                                    >
                                        <Stack
                                            direction="row"
                                            align="center"
                                            gap="3x"
                                        >
                                            <Box width="150px">
                                                <Text
                                                    size="200"
                                                    type="title"
                                                    data-id={`new-attachment-name-${file.name}`}
                                                >
                                                    {file.name}
                                                </Text>
                                            </Box>
                                            {file.creationDate && (
                                                <Text
                                                    type="body"
                                                    size="100"
                                                    colorScheme="faded"
                                                >
                                                    {t`Created on`}{' '}
                                                    <DateTime
                                                        date={file.creationDate}
                                                        format="sentence"
                                                        textProps={{
                                                            type: 'body',
                                                            size: '100',
                                                            colorScheme:
                                                                'faded',
                                                        }}
                                                    />
                                                </Text>
                                            )}
                                        </Stack>
                                        <Stack direction="row" gap="2x">
                                            <Tooltip
                                                isInteractive
                                                text={t`Download file`}
                                            >
                                                <Button
                                                    isIconOnly
                                                    startIconName="Download"
                                                    label={t`Download file`}
                                                    type="button"
                                                    level="tertiary"
                                                    colorScheme="neutral"
                                                    size="sm"
                                                    onClick={() => {
                                                        downloadBlob(
                                                            file.file.file,
                                                            file.name,
                                                        );
                                                    }}
                                                />
                                            </Tooltip>
                                            <Tooltip
                                                isInteractive
                                                text={t`Remove file`}
                                            >
                                                <Button
                                                    isIconOnly
                                                    startIconName="Trash"
                                                    label={t`Remove file`}
                                                    type="button"
                                                    level="tertiary"
                                                    colorScheme="danger"
                                                    size="sm"
                                                    data-id={`remove-new-attachment-${file.name}`}
                                                    onClick={() => {
                                                        setFiles((prev) =>
                                                            prev.filter(
                                                                (_, i) =>
                                                                    i !== index,
                                                            ),
                                                        );
                                                    }}
                                                />
                                            </Tooltip>
                                        </Stack>
                                    </Stack>
                                }
                            />
                        ))}
                    </StackedList>
                </Box>
            )}

            {attachmentConfig.showAddAttachment === 'modal' && (
                <Stack direction="row" gap="md" justify="start" pb="sm">
                    <Button
                        hasPadding={false}
                        level="tertiary"
                        data-id="add-attachment-button"
                        label={
                            attachmentConfig.allowMultipleFiles
                                ? t`Add attachments`
                                : t`Add attachment`
                        }
                        onClick={() => {
                            modalController.openModal({
                                id: UTILITIES_NOTES_COMMENT_ATTACHMENT_FORM_MODAL_ID,
                                content: modalContent,
                                size: 'lg',
                                centered: true,
                                disableClickOutsideToClose: true,
                            });
                        }}
                    />
                </Stack>
            )}

            <Stack direction="row" gap="md" justify="start" pb="10x">
                <Button
                    isLoading={isLoading}
                    // TODO build this into the config
                    type="submit"
                    label={actionButtonLabel}
                    a11yLoadingLabel={t`Saving note, please wait`}
                    data-id="latest-run-button"
                />
                {editMode ? (
                    <Button
                        type="button"
                        label={t`Cancel`}
                        level="secondary"
                        data-id="latest-run-button"
                        cosmosUseWithCaution_isDisabled={isLoading}
                        onClick={() => onCancel?.(getCurrentFormValues)}
                    />
                ) : null}
            </Stack>
        </FormWrapper>
    );
};
