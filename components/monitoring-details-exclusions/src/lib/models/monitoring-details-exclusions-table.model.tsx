import React from 'react';
import {
    closeMonitorUpdateExclusionReasonBulkModal,
    openMonitorUpdateExclusionReasonBulkModal,
} from '@components/monitor-update-exclusion-reason-modal';
import {
    sharedMonitoringDetailsExclusionsController,
    sharedMonitoringFindingsExclusionReasonController,
} from '@controllers/monitoring-details';
import type {
    BulkAction,
    DatatableRef,
    DatatableRowSelectionState,
    ExtendedDataTableColumnDef,
} from '@cosmos/components/datatable';
import type { MonitorExclusionResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable } from '@globals/mobx';
import { MonitoringDetailsAccountNameCell } from '../cells/monitoring-details-account-name-cell';
import { MonitoringDetailsActionsCell } from '../cells/monitoring-details-actions-cell';
import { MonitoringDetailsConnectionAliasCell } from '../cells/monitoring-details-connection-alias-cell';
import { MonitoringDetailsConnectionCell } from '../cells/monitoring-details-connection-cell';
import { MonitoringDetailsDateExcludedCell } from '../cells/monitoring-details-date-excluded-cell';
import { MonitoringDetailsExcludedByCell } from '../cells/monitoring-details-excluded-by-cell';
import { MonitoringDetailsGroupCell } from '../cells/monitoring-details-group-cell';
import { MonitoringDetailsRegionCell } from '../cells/monitoring-details-region-cell';

class MonitoringDetailsExclusionsTableModel {
    selectedTargetIds: number[] = [];
    isAllRowsSelected = false;
    datatableRef: React.RefObject<DatatableRef> =
        React.createRef<DatatableRef>();

    constructor() {
        makeAutoObservable(this);
    }

    get bulkActions(): BulkAction[] {
        return [
            {
                actionType: 'button',
                id: 'bulk-actions-update-exclusion',
                typeProps: {
                    label: t`Update reason`,
                    level: 'tertiary',
                    onClick: action(() => {
                        openMonitorUpdateExclusionReasonBulkModal({
                            onConfirm: (reason: string) => {
                                const { testId } =
                                    sharedMonitoringDetailsExclusionsController;

                                if (!testId) {
                                    return;
                                }

                                sharedMonitoringFindingsExclusionReasonController.updateExclusionReasonBulk(
                                    testId,
                                    this.selectedTargetIds,
                                    reason,
                                    () => {
                                        closeMonitorUpdateExclusionReasonBulkModal();
                                        this.datatableRef.current?.resetRowSelection();
                                    },
                                );
                            },
                        });
                    }),
                },
            },
        ];
    }

    handleRowSelection = (
        currentRowSelectionState: DatatableRowSelectionState,
    ): void => {
        const { selectedRows, isAllRowsSelected } = currentRowSelectionState;
        const selectedIds = Object.keys(selectedRows);

        this.selectedTargetIds = selectedIds.map(Number);
        this.isAllRowsSelected = isAllRowsSelected;
    };

    get columns(): ExtendedDataTableColumnDef<MonitorExclusionResponseDto>[] {
        return [
            {
                id: 'ACTIONS',
                header: '',
                enableSorting: false,
                isActionColumn: true,
                cell: MonitoringDetailsActionsCell,
            },
            {
                id: 'TARGET_NAME',
                header: t`Resource name`,
                accessorKey: 'displayName',
                enableSorting: true,
                isActionColumn: true,
            },
            {
                id: 'REGION',
                header: t`Region`,
                enableSorting: false,
                isActionColumn: true,
                cell: MonitoringDetailsRegionCell,
            },
            {
                id: 'CONNECTION',
                header: t`Connection`,
                enableSorting: false,
                isActionColumn: true,
                cell: MonitoringDetailsConnectionCell,
            },
            {
                id: 'CONNECTION_CLIENT_ALIAS',
                header: t`Connection ID / alias`,
                enableSorting: true,
                isActionColumn: true,
                cell: MonitoringDetailsConnectionAliasCell,
            },
            {
                id: 'ACCOUNT_NAME_ID',
                header: t`Account name / ID`,
                enableSorting: false,
                isActionColumn: true,
                cell: MonitoringDetailsAccountNameCell,
            },
            {
                id: 'GROUP',
                header: t`Group`,
                enableSorting: true,
                isActionColumn: true,
                cell: MonitoringDetailsGroupCell,
            },
            {
                id: 'CREATED_BY',
                header: t`Excluded by`,
                enableSorting: true,
                isActionColumn: true,
                cell: MonitoringDetailsExcludedByCell,
            },
            {
                id: 'START_DATE',
                header: t`Date excluded`,
                enableSorting: true,
                isActionColumn: true,
                cell: MonitoringDetailsDateExcludedCell,
            },
            {
                id: 'EXCLUSION_REASON',
                header: t`Reason`,
                accessorKey: 'reason',
                enableSorting: true,
                isActionColumn: true,
            },
        ];
    }
}

export const sharedMonitoringDetailsExclusionsTableModel =
    new MonitoringDetailsExclusionsTableModel();
