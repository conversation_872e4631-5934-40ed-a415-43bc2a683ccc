import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import type { MonitorCodebaseExclusionResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { observer } from '@globals/mobx';
import { formatDate } from '@helpers/date-time';
import { getInitials } from '@helpers/formatters';
import { Form, useFormSubmit } from '@ui/forms';
import { closeMonitorUpdateCodebaseExclusionReasonModal } from './helpers/monitor-update-exclusion-reason-modal.helper';
import { monitorUpdateExclusionReasonModalModel } from './models/monitor-update-exclusion-reason-modal.model';

interface MonitorUpdateCodebaseExclusionReasonModalProps {
    exclusion: MonitorCodebaseExclusionResponseDto;
    onConfirm: (reason: string) => void;
    'data-id'?: string;
}

export const MonitorUpdateCodebaseExclusionReasonModal = observer(
    ({
        exclusion,
        onConfirm,
        'data-id': dataId = 'MonitorUpdateCodebaseExclusionReasonModal',
    }: MonitorUpdateCodebaseExclusionReasonModalProps): React.JSX.Element => {
        const { formRef, triggerSubmit } = useFormSubmit();

        return (
            <>
                <Modal.Header
                    title={t`Update exclusion reason`}
                    closeButtonAriaLabel={t`Close modal`}
                    onClose={closeMonitorUpdateCodebaseExclusionReasonModal}
                />
                <Modal.Body>
                    <Stack direction="column" gap="lg">
                        <KeyValuePair
                            type="USER"
                            label={t`Excluded by`}
                            value={{
                                username: exclusion.createdBy,
                                avatarProps: {
                                    fallbackText: getInitials(
                                        exclusion.createdBy,
                                    ),
                                    imgSrc: '',
                                    imgAlt: exclusion.createdBy,
                                },
                            }}
                        />
                        <KeyValuePair
                            label={t`Date excluded`}
                            value={formatDate('table', exclusion.excludedAt)}
                        />
                        <Form
                            hasExternalSubmitButton
                            ref={formRef}
                            formId="monitoring-codebase-exclusion-form"
                            data-id={`${dataId}-form`}
                            schema={monitorUpdateExclusionReasonModalModel.getFormSchema(
                                exclusion.reason,
                            )}
                            onSubmit={monitorUpdateExclusionReasonModalModel.getFormSubmitHandler(
                                onConfirm,
                            )}
                        />
                    </Stack>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Cancel`,
                            level: 'secondary',
                            onClick:
                                closeMonitorUpdateCodebaseExclusionReasonModal,
                        },
                        {
                            label: t`Update reason`,
                            level: 'primary',
                            onClick: () => {
                                triggerSubmit().catch((error) => {
                                    logger.error({
                                        message:
                                            'Failed to submit codebase exclusion reason form',
                                        errorObject: {
                                            message:
                                                error instanceof Error
                                                    ? error.message
                                                    : String(error),
                                            statusCode: 'unknown',
                                        },
                                    });
                                });
                            },
                        },
                    ]}
                />
            </>
        );
    },
);
