import { isError } from 'lodash-es';
import { z } from 'zod';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { action, makeAutoObservable } from '@globals/mobx';
import type { FormSchema, FormValues } from '@ui/forms';

class MonitorUpdateExclusionReasonModalModel {
    constructor() {
        makeAutoObservable(this);
    }

    getFormSchema(initialValue = ''): FormSchema {
        return {
            reason: {
                type: 'textarea',
                label: t`Reason for exclusion`,
                helpText: t`Please tell us why this isn’t relevant to the test.`,
                validator: z
                    .string()
                    .min(1, t`Exclusion reason is required`)
                    .max(
                        1000,
                        t`Exclusion reason must be 1000 characters or less`,
                    )
                    .refine((val) => val.trim().length > 0, {
                        message: t`Exclusion reason is required`,
                    }),
                initialValue,
                rows: 3,
                maxCharacters: 1000,
            },
        };
    }

    submitForm = (values: FormValues, onConfirm: (reason: string) => void) => {
        const reason = values.reason as string;

        if (!reason.trim()) {
            return;
        }

        try {
            onConfirm(reason.trim());
        } catch (error) {
            logger.error({
                message: 'Failed to confirm exclusion reason',
                errorObject: {
                    message: isError(error) ? error.message : String(error),
                    statusCode: 'unknown',
                },
            });
        }
    };

    handleSubmit = action(this.submitForm);

    getFormSubmitHandler = (onConfirm: (reason: string) => void) => {
        return (values: FormValues) => {
            this.handleSubmit(values, onConfirm);
        };
    };
}

export const monitorUpdateExclusionReasonModalModel =
    new MonitorUpdateExclusionReasonModalModel();
