import { modalController } from '@controllers/modal';
import type {
    MonitorCodebaseExclusionResponseDto,
    MonitorExclusionResponseDto,
} from '@globals/api-sdk/types';
import { action } from '@globals/mobx';
import { MonitorUpdateCodebaseExclusionReasonModal } from '../monitor-update-codebase-exclusion-reason-modal.component';
import { MonitorUpdateExclusionReasonBulkModal } from '../monitor-update-exclusion-reason-bulk-modal.component';
import { MonitorUpdateExclusionReasonModal } from '../monitor-update-exclusion-reason-modal.component';

export const closeMonitorUpdateExclusionReasonModal = action((): void => {
    modalController.closeModal('monitor-update-exclusion-reason-modal');
});

export const closeMonitorUpdateExclusionReasonBulkModal = action((): void => {
    modalController.closeModal('monitor-update-exclusion-reason-bulk-modal');
});

export const closeMonitorUpdateCodebaseExclusionReasonModal = action(
    (): void => {
        modalController.closeModal(
            'monitor-update-codebase-exclusion-reason-modal',
        );
    },
);

export const openMonitorUpdateExclusionReasonModal = action(
    (
        exclusion: MonitorExclusionResponseDto,
        onConfirm: (reason: string) => void,
    ): void => {
        modalController.openModal({
            id: 'monitor-update-exclusion-reason-modal',
            content: () => (
                <MonitorUpdateExclusionReasonModal
                    data-id={'MonitorUpdateExclusionReasonModal'}
                    exclusion={exclusion}
                    onConfirm={onConfirm}
                />
            ),
            centered: true,
            disableClickOutsideToClose: true,
            size: 'md',
        });
    },
);

export const openMonitorUpdateExclusionReasonBulkModal = action(
    ({ onConfirm }: { onConfirm: (reason: string) => void }): void => {
        modalController.openModal({
            id: 'monitor-update-exclusion-reason-bulk-modal',
            content: () => (
                <MonitorUpdateExclusionReasonBulkModal
                    data-id={'MonitorUpdateExclusionReasonBulkModal'}
                    onConfirm={onConfirm}
                />
            ),
            centered: true,
            disableClickOutsideToClose: true,
            size: 'md',
        });
    },
);

export const openMonitorUpdateCodebaseExclusionReasonModal = action(
    ({
        exclusion,
        onConfirm,
    }: {
        exclusion: MonitorCodebaseExclusionResponseDto;
        onConfirm: (reason: string) => void;
    }): void => {
        modalController.openModal({
            id: 'monitor-update-codebase-exclusion-reason-modal',
            content: () => (
                <MonitorUpdateCodebaseExclusionReasonModal
                    data-id={'MonitorUpdateCodebaseExclusionReasonModal'}
                    exclusion={exclusion}
                    onConfirm={onConfirm}
                />
            ),
            centered: true,
            disableClickOutsideToClose: true,
            size: 'md',
        });
    },
);
