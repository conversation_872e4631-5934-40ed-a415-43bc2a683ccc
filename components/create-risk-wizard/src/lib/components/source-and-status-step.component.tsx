import { sharedCreateRiskMutationController } from '@controllers/risk';
import { sharedVendorsRisksMutationController } from '@controllers/vendors';
import { Stack } from '@cosmos/components/stack';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { action, observer } from '@globals/mobx';
import { Form } from '@ui/forms';
import { getRiskSourceAndStatusSchema } from '../helpers/get-source-and-status-schema.helper';

export const SourceAndStatusStep = observer(
    ({
        formRef,
        vendorId,
    }: {
        formRef: React.ForwardedRef<HTMLFormElement>;
        vendorId?: number;
    }): React.JSX.Element => {
        const { isVendorRiskManagementProEnabled } = sharedFeatureAccessModel;
        const storedValues = vendorId
            ? sharedVendorsRisksMutationController.getMutatedStateForStep(
                  'sourceAndStatus',
              )
            : sharedCreateRiskMutationController.getMutatedStateForStep(
                  'sourceAndStatus',
              );

        return (
            <Stack
                data-testid="SourceAndStatusStep"
                data-id="LniIy7Oa"
                direction="column"
                width="100%"
                gap="2xl"
            >
                <Form
                    hasExternalSubmitButton
                    data-id="source-and-status-form"
                    ref={formRef}
                    formId="create-risk-wizard-form-id-source-and-status"
                    schema={getRiskSourceAndStatusSchema(
                        vendorId,
                        isVendorRiskManagementProEnabled,
                        storedValues,
                    )}
                    onSubmit={action((values) => {
                        if (vendorId) {
                            sharedVendorsRisksMutationController.setCreateRiskWizardDataOverride(
                                values,
                            );
                        } else {
                            sharedCreateRiskMutationController.setCreateRiskWizardDataOverride(
                                values,
                            );
                        }
                    })}
                />
            </Stack>
        );
    },
);
