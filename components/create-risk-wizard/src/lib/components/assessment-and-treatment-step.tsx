import { sharedCreateRiskMutationController } from '@controllers/risk';
import { sharedVendorsRisksMutationController } from '@controllers/vendors';
import { Stack } from '@cosmos/components/stack';
import { action, observer } from '@globals/mobx';
import { Form } from '@ui/forms';
import { sharedRiskAssessmentAndTreatmentFormModel } from '../models/risk-assessment-and-treatment-form.model';

export const AssessmentAndTreatmentStep = observer(
    ({
        formRef,
        vendorId,
    }: {
        formRef: React.ForwardedRef<HTMLFormElement>;
        vendorId?: number;
    }): React.JSX.Element => {
        const storedValues = vendorId
            ? sharedVendorsRisksMutationController.getMutatedStateForStep(
                  'assessmentAndTreatment',
              )
            : sharedCreateRiskMutationController.getMutatedStateForStep(
                  'assessmentAndTreatment',
              );

        return (
            <Stack gap="lg" direction="column" width="100%" data-id="5K9OxtV-">
                <Form
                    hasExternalSubmitButton
                    data-id="assessment-and-treatment-form"
                    ref={formRef}
                    formId="create-risk-wizard-form-id-assessment-and-treatment"
                    schema={sharedRiskAssessmentAndTreatmentFormModel.getSchema(
                        storedValues,
                    )}
                    onSubmit={action((values) => {
                        if (vendorId) {
                            sharedVendorsRisksMutationController.setCreateRiskWizardDataOverride(
                                values,
                            );
                        } else {
                            sharedCreateRiskMutationController.setCreateRiskWizardDataOverride(
                                values,
                            );
                        }
                    })}
                />
            </Stack>
        );
    },
);
