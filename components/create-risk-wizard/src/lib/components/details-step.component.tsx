import { sharedCreateRiskMutationController } from '@controllers/risk';
import { sharedVendorsRisksMutationController } from '@controllers/vendors';
import { Stack } from '@cosmos/components/stack';
import { action, observer } from '@globals/mobx';
import { Form } from '@ui/forms';
import { sharedRiskDetailsFormModel } from '../models/risk-details-form.model';

export const DetailsStep = observer(
    ({
        formRef,
        vendorId,
    }: {
        formRef: React.ForwardedRef<HTMLFormElement>;
        vendorId?: number;
    }): React.JSX.Element => {
        return (
            <Stack
                data-testid="DetailsStep"
                gap="lg"
                direction="column"
                width="100%"
                data-id="nAg7dUow"
            >
                <Form
                    hasExternalSubmitButton
                    data-id="details-form-data-id"
                    ref={formRef}
                    formId="create-risk-wizard-form-id-details"
                    schema={sharedRiskDetailsFormModel.getFormSchema}
                    onSubmit={action((values) => {
                        if (vendorId) {
                            sharedVendorsRisksMutationController.setCreateRiskWizardDataOverride(
                                values,
                            );
                        } else {
                            sharedCreateRiskMutationController.setCreateRiskWizardDataOverride(
                                values,
                            );
                        }
                    })}
                />
            </Stack>
        );
    },
);
