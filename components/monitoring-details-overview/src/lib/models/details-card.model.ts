import { isError } from 'lodash-es';
import {
    activeMonitoringController,
    sharedMonitoringDetailsUpdateMutationController,
} from '@controllers/monitoring-details';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import type { Action } from '@cosmos/components/action-stack';
import type {
    MonitorV2ControlTestInstanceDetailsResponseDto,
    MonitorV2ControlTestInstanceOverviewResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable } from '@globals/mobx';
import { getSourceLabel } from '../helpers/source-label';

export class DetailsCardModel {
    constructor() {
        makeAutoObservable(this);
    }

    get monitorDetailsData(): MonitorV2ControlTestInstanceDetailsResponseDto | null {
        return activeMonitoringController.monitorDetailsData;
    }

    get isMonitorLoading(): boolean {
        return activeMonitoringController.isMonitorLoading;
    }

    get testDetails(): MonitorV2ControlTestInstanceOverviewResponseDto | null {
        return sharedMonitoringTestDetailsController.testDetails;
    }

    get isTestDetailsLoading(): boolean {
        return sharedMonitoringTestDetailsController.isLoading;
    }

    get isLoading(): boolean {
        return this.isMonitorLoading || this.isTestDetailsLoading;
    }

    get hasData(): boolean {
        return Boolean(this.monitorDetailsData) || Boolean(this.testDetails);
    }

    get description(): string {
        return (
            this.testDetails?.monitorInstances[0]
                ?.evidenceCollectionDescription || ''
        );
    }

    get sourceValue(): string {
        return (
            this.testDetails?.source || this.monitorDetailsData?.source || ''
        );
    }

    get source(): string {
        return getSourceLabel(this.sourceValue);
    }

    get checkStatus(): string {
        return (
            this.testDetails?.checkStatus ||
            this.monitorDetailsData?.checkStatus ||
            'ENABLED'
        );
    }

    get name(): string {
        return this.testDetails?.name || this.monitorDetailsData?.name || '';
    }

    get isCustomTest(): boolean {
        return this.sourceValue === 'CUSTOM';
    }

    get isCustomDraftOrPublished(): boolean {
        return this.isCustomTest;
    }

    get showHelpArticle(): boolean {
        return this.sourceValue === 'DRATA';
    }

    get testId(): number | undefined {
        return this.testDetails?.testId || this.monitorDetailsData?.testId;
    }

    getCardActions = (
        isEditing: boolean,
        triggerSubmit: () => Promise<boolean>,
        handleEdit: () => void,
        handleCancel: () => void,
    ): Action[] => {
        if (!this.isCustomDraftOrPublished) {
            return [];
        }

        if (isEditing) {
            const actions: Action[] = [
                {
                    id: 'save',
                    actionType: 'button',
                    typeProps: {
                        label: t`Save`,
                        level: 'primary',
                        isLoading:
                            sharedMonitoringDetailsUpdateMutationController.isUpdating,
                        onClick: (): void => {
                            triggerSubmit().catch((error: unknown) => {
                                const errorMessage = isError(error)
                                    ? error.message
                                    : 'Unknown error occurred';

                                logger.error(
                                    `Failed to submit form: ${errorMessage}`,
                                );
                            });
                        },
                    },
                },
            ];

            // Only add cancel button if not updating
            if (!sharedMonitoringDetailsUpdateMutationController.isUpdating) {
                actions.push({
                    id: 'cancel',
                    actionType: 'button',
                    typeProps: {
                        label: t`Cancel`,
                        level: 'secondary',
                        onClick: handleCancel,
                    },
                });
            }

            return actions;
        }

        return [
            {
                id: 'edit',
                actionType: 'button',
                typeProps: {
                    label: t`Edit`,
                    level: 'secondary',
                    onClick: handleEdit,
                },
            },
        ];
    };

    handleSubmit = (
        values: { name: string; description: string | undefined },
        onSuccess: () => void,
    ): void => {
        if (!this.testId) {
            logger.error('No test ID available for update');

            return;
        }

        sharedMonitoringDetailsUpdateMutationController.updateMonitorDetails(
            this.testId,
            {
                name: values.name,
                description: values.description,
            },
            onSuccess,
        );
    };
}

export const sharedDetailsCardModel = new DetailsCardModel();
