import { get, isEmpty } from 'lodash-es';
import { sharedEventsController } from '@controllers/events';
import { sharedWorkspaceMonitorsController } from '@controllers/workspace-monitors';
import type { ColorScheme } from '@cosmos/components/metadata';
import type { EventResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { formatDate } from '@helpers/date-time';
import { getLatestResultErrorMessage } from '../helpers/latest-result-error-message.helper';

class LatestResultsCardModel {
    constructor() {
        makeAutoObservable(this);
    }

    get colorScheme(): ColorScheme | undefined {
        const { workspaceMonitorTestOverview } =
            sharedWorkspaceMonitorsController;

        if (!workspaceMonitorTestOverview) {
            return undefined;
        }

        if (workspaceMonitorTestOverview.checkResultStatus === 'FAILED') {
            return 'critical';
        }

        if (workspaceMonitorTestOverview.checkResultStatus === 'ERROR') {
            return 'warning';
        }

        if (workspaceMonitorTestOverview.checkResultStatus === 'PASSED') {
            return 'success';
        }

        return 'neutral';
    }

    get label(): string {
        const { workspaceMonitorTestOverview } =
            sharedWorkspaceMonitorsController;

        if (!workspaceMonitorTestOverview) {
            return '';
        }

        if (workspaceMonitorTestOverview.checkResultStatus === 'FAILED') {
            return t`Failed`;
        }

        if (workspaceMonitorTestOverview.checkResultStatus === 'ERROR') {
            return t`Error`;
        }

        if (workspaceMonitorTestOverview.checkResultStatus === 'PASSED') {
            return t`Passed`;
        }

        return workspaceMonitorTestOverview.name;
    }

    get description(): string {
        const { workspaceMonitorTestOverview } =
            sharedWorkspaceMonitorsController;

        if (!workspaceMonitorTestOverview) {
            return '';
        }

        if (workspaceMonitorTestOverview.checkResultStatus === 'FAILED') {
            return this.failedDescription;
        }

        if (workspaceMonitorTestOverview.checkResultStatus === 'ERROR') {
            return this.errorDescription;
        }

        if (workspaceMonitorTestOverview.checkResultStatus === 'PASSED') {
            return this.passedDescription;
        }

        return '';
    }

    private get passedDescription(): string {
        const { workspaceMonitorTestOverview } =
            sharedWorkspaceMonitorsController;

        if (!workspaceMonitorTestOverview) {
            return '';
        }

        return workspaceMonitorTestOverview.description;
    }

    private get failedDescription(): string {
        const { workspaceMonitorTestOverview } =
            sharedWorkspaceMonitorsController;

        if (!workspaceMonitorTestOverview) {
            return '';
        }

        return get(
            workspaceMonitorTestOverview,
            'monitorInstances[0].failedTestDescription',
            '',
        );
    }

    private get errorDescription(): string {
        const regex = /.*is not authorized to perform*/;
        const { events } = sharedEventsController;
        const { workspaceMonitorTestOverview } =
            sharedWorkspaceMonitorsController;

        if (!workspaceMonitorTestOverview) {
            return '';
        }

        const permissionErroredEvents = events.filter(
            (event: EventResponseDto) => {
                const { issues } = event;

                return issues.some((issue) =>
                    regex.test(issue.message as string),
                );
            },
        );

        return getLatestResultErrorMessage({
            hasPermissionIssues: !isEmpty(permissionErroredEvents),
            hasConnectionError: false,
            source: workspaceMonitorTestOverview.source,
        });
    }

    get helpMessage(): string {
        const { workspaceMonitorTestOverview } =
            sharedWorkspaceMonitorsController;

        if (!workspaceMonitorTestOverview) {
            return '';
        }

        if (workspaceMonitorTestOverview.checkResultStatus === 'ERROR') {
            return t`Need help? Click the question mark in the bottom-right to contact support.`;
        }

        return '';
    }

    get lastCheckDate(): string | undefined {
        const { workspaceMonitorTestOverview } =
            sharedWorkspaceMonitorsController;

        if (!workspaceMonitorTestOverview) {
            return undefined;
        }

        const { lastCheck } = workspaceMonitorTestOverview;

        if (!lastCheck) {
            return undefined;
        }

        const formattedDate = formatDate('overdue', lastCheck);

        return t`Tested ${formattedDate}`;
    }

    get isMonitoringAiSummaryCardDisplayed(): boolean {
        const { workspaceMonitorTestOverview } =
            sharedWorkspaceMonitorsController;

        if (!workspaceMonitorTestOverview) {
            return false;
        }

        const { isReleaseMonitoringAiFailureSummaryEnabled } =
            sharedFeatureAccessModel;
        const { events } = sharedEventsController;
        const { runMode, monitorInstances } = workspaceMonitorTestOverview;

        const isAP2 = runMode === 'AP2';
        const hasFailedInstance = monitorInstances.some(
            (instance) =>
                instance.checkResultStatus === 'FAILED' &&
                instance.metadata.some((metadata) => !isEmpty(metadata.fail)),
        );
        const hasValidSummaryAiEvents = events.some(
            (event) => event.status === 'FAILED' && event.haveFailedResources,
        );

        return (
            isReleaseMonitoringAiFailureSummaryEnabled &&
            isAP2 &&
            hasFailedInstance &&
            hasValidSummaryAiEvents
        );
    }
}

export const sharedLatestResultsCardModel = new LatestResultsCardModel();
