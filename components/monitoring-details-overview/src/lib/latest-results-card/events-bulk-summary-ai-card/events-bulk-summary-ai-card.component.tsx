import { AICard } from '@cosmos-lab/components/ai-card';
import { observer } from '@globals/mobx';
import { sharedEventsBulkSummaryAICardModel } from './events-bulk-summary-ai-card.model';

export const EventsBulkSummaryAICard = observer((): React.JSX.Element => {
    const { title, isLoading, body } = sharedEventsBulkSummaryAICardModel;

    return (
        <AICard
            title={title}
            isLoading={isLoading}
            data-id="events-bulk-summary-ai-card"
            body={body}
        />
    );
});
