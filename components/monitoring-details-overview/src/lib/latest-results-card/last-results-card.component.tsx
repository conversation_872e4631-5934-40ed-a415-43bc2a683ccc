import { Card } from '@cosmos/components/card';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { EventsBulkSummaryAICard } from './events-bulk-summary-ai-card/events-bulk-summary-ai-card.component';
import { sharedLatestResultsCardModel } from './latest-results-card.model';

export const LastResultsCard = observer((): React.JSX.Element => {
    const {
        description,
        helpMessage,
        label,
        colorScheme,
        lastCheckDate,
        isMonitoringAiSummaryCardDisplayed,
    } = sharedLatestResultsCardModel;

    return (
        <Card
            data-id="overview-last-results-card"
            title={t`Latest result`}
            data-testid="LastResultsCard"
            body={
                <Stack gap="lg" direction="column">
                    <Stack gap="lg" direction="column">
                        <Stack direction="row" gap="md">
                            <Metadata
                                label={label}
                                type="status"
                                colorScheme={colorScheme}
                            />
                            <Text colorScheme="faded">{lastCheckDate}</Text>
                        </Stack>
                        <Text>{description}</Text>
                        <Text>{helpMessage}</Text>
                    </Stack>
                    {isMonitoringAiSummaryCardDisplayed && (
                        <EventsBulkSummaryAICard />
                    )}
                </Stack>
            }
        />
    );
});
