import { z } from 'zod';
import type { QuestionnaireVendorResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import type { FormSchema } from '@ui/forms';

export const sendQuestionnaireModalFormSchema = (
    questionnaires: QuestionnaireVendorResponseDto[],
    contactsEmail?: string | null,
): FormSchema => ({
    questionnaires: {
        type: 'select',
        initialValue: undefined,
        label: t`Choose questionnaires to send`,
        options: questionnaires.map((questionnaire) => ({
            id: questionnaire.id.toString(),
            label: questionnaire.title,
            value: questionnaire.id.toString(),
        })),
        placeholder: t`Start typing name of questionnaire...`,
        isOptional: false,
    },
    sendTo: {
        type: 'text',
        initialValue: contactsEmail || '',
        label: t`Send to`,
        helpText: t`You can add up to 5 recipients separated by comma e.g. <EMAIL>, <EMAIL>`,
        isOptional: false,
        validator: z
            .string({ message: t`At least one email is required` })
            .min(1, { message: t`At least one email is required` })
            .refine(
                (val) => {
                    const emails = val.split(',').map((email) => email.trim());

                    return emails.every(
                        (email) => z.string().email().safeParse(email).success,
                    );
                },
                {
                    message: t`Please enter valid email addresses separated by commas`,
                },
            ),
    },
    message: {
        type: 'textarea',
        initialValue: t`Hi,\n\nWe'd like to conduct a security review and would like some information from you. Use this link to complete the questionnaire.\n\nThank you.`,
        label: t`Message to the vendor`,
        isOptional: false,
        validator: z.string().min(1, { message: t`Message is required` }),
    },
});
