import { modalController } from '@controllers/modal';
import {
    sharedVendorsCreateVendorController,
    sharedVendorsDetailsController,
    sharedVendorsTypeformQuestionnairesController,
} from '@controllers/vendors';
import { Button } from '@cosmos/components/button';
import { Modal } from '@cosmos/components/modal';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getParentRoute } from '@helpers/path';
import { useLocation } from '@remix-run/react';
import { Form, useFormSubmit } from '@ui/forms';
import { SEND_QUESTIONNAIRE_MODAL_ID } from '../constants/send-questionnaire-modal.constant';
import { sendQuestionnaireModalFormSchema } from '../schemas/send-questionnaire-modal-form.schema';

const FORM_ID = 'map-control-to-evidence-modal-form-id';

export const SendQuestionnaireModal = observer((): React.JSX.Element => {
    const { allVendorsQuestionnaires, isLoading } =
        sharedVendorsTypeformQuestionnairesController;
    const { saveVendorProspectiveQuestionnaires } =
        sharedVendorsCreateVendorController;
    const { vendorDetails } = sharedVendorsDetailsController;
    const location = useLocation();
    const parentRoute = getParentRoute(location.pathname, 2);
    const { formRef, triggerSubmit } = useFormSubmit();

    return (
        <>
            <Modal.Header
                title={t`Send questionnaire`}
                closeButtonAriaLabel={t`Close send questionnaire modal`}
                onClose={() => {
                    modalController.closeModal(SEND_QUESTIONNAIRE_MODAL_ID);
                }}
            />
            <Modal.Body>
                {isLoading ? (
                    <Skeleton barCount={10} />
                ) : (
                    <Stack gap="xl" direction="column">
                        <Form
                            hasExternalSubmitButton
                            ref={formRef}
                            formId={FORM_ID}
                            data-id="send-questionnaire-modal-form-id"
                            schema={sendQuestionnaireModalFormSchema(
                                allVendorsQuestionnaires,
                                vendorDetails?.contactsEmail,
                            )}
                            onSubmit={(values) => {
                                saveVendorProspectiveQuestionnaires(values);
                            }}
                        />
                        <Button
                            label={t`Set follow-up reminders in settings`}
                            size="sm"
                            level="tertiary"
                            href={`${parentRoute}/settings`}
                        />
                    </Stack>
                )}
            </Modal.Body>
            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Preview`,
                        level: 'secondary',
                        onClick: () => {
                            // TODO: Blocked by https://drata.atlassian.net/browse/ENG-70209
                        },
                    },
                    {
                        label: t`Confirm`,
                        level: 'primary',
                        colorScheme: 'primary',
                        onClick: () => {
                            triggerSubmit().catch(() => {
                                console.error('Failed to submit form');
                            });
                        },
                    },
                ]}
            />
        </>
    );
});
