import React, { useMemo } from 'react';
import { Box } from '@cosmos/components/box';
import { CheckboxField } from '@cosmos/components/checkbox-field';
import { Grid } from '@cosmos/components/grid';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { dimension2x } from '@cosmos/constants/tokens';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedCompareControlFieldsModalModel } from '@models/controls';
import { UniversalFormField, useFormContext } from '@ui/forms';
import {
    COMPARE_CONTROL_FIELDS_FORM_ID,
    getCompareControlFieldsHeaderTitle,
    getCompareControlFieldsSkeletonProps,
} from '../../constants/compare-control-fields-modal.constants';

export const CompareControlFieldsTable = observer((): React.JSX.Element => {
    const {
        isLoading,
        fieldsKeysToRender,
        templateValues,
        currentFormValues,
        fieldsWithDifferencesMap,
        getApplyAll,
        handleApplyAllChange,
    } = sharedCompareControlFieldsModalModel;

    const { setValue, watch } = useFormContext();

    const formValues = watch();

    const applyAllValue = useMemo(
        () => getApplyAll(formValues),
        [getApplyAll, formValues],
    );

    return (
        <Grid
            columns={`1fr 1fr ${dimension2x} 0.4fr`}
            gapY="md"
            data-testid="CompareControlFieldsTable"
            data-id="NM2HQi9t"
        >
            <Box>
                <Text type="title" size="100">
                    <Trans>Current control</Trans>
                </Text>
            </Box>
            <Box>
                <Text type="title" size="100">
                    {t`Drata's template`}
                </Text>
            </Box>
            <Box gridColumnStart="4">
                <Text type="title" size="100">
                    <Trans>Selections</Trans>
                </Text>
            </Box>
            <Box gridColumnStart="4">
                {!isLoading && (
                    <CheckboxField
                        formId={COMPARE_CONTROL_FIELDS_FORM_ID}
                        label={t`Apply all`}
                        name="applyAll"
                        value="applyAll"
                        checked={applyAllValue}
                        onChange={(checked: boolean) => {
                            handleApplyAllChange(checked, setValue);
                        }}
                    />
                )}
            </Box>
            {fieldsKeysToRender.map((fieldKey) => {
                return (
                    <React.Fragment key={fieldKey}>
                        <Stack direction="column" gap="sm">
                            <Box px="xl">
                                <Text type="code" size="100">
                                    {getCompareControlFieldsHeaderTitle(
                                        fieldKey,
                                    )}
                                </Text>
                            </Box>
                            <Box
                                px="xl"
                                backgroundColor={
                                    !isLoading &&
                                    fieldsWithDifferencesMap.get(fieldKey)
                                        ? 'criticalBackgroundMild'
                                        : undefined
                                }
                            >
                                {isLoading ? (
                                    <Skeleton
                                        {...getCompareControlFieldsSkeletonProps(
                                            fieldKey,
                                        )}
                                    />
                                ) : (
                                    <Text as="div" type="code" size="100">
                                        {currentFormValues[fieldKey]}
                                    </Text>
                                )}
                            </Box>
                        </Stack>
                        <Stack direction="column" gap="sm">
                            <Box px="xl">
                                <Text type="code" size="100">
                                    {getCompareControlFieldsHeaderTitle(
                                        fieldKey,
                                    )}
                                </Text>
                            </Box>
                            <Box
                                px="xl"
                                backgroundColor={
                                    !isLoading &&
                                    fieldsWithDifferencesMap.get(fieldKey)
                                        ? 'successBackgroundMild'
                                        : undefined
                                }
                            >
                                {isLoading ? (
                                    <Skeleton
                                        {...getCompareControlFieldsSkeletonProps(
                                            fieldKey,
                                        )}
                                    />
                                ) : (
                                    <Text as="div" type="code" size="100">
                                        {templateValues?.[fieldKey]}
                                    </Text>
                                )}
                            </Box>
                        </Stack>
                        <Box gridColumnStart="4">
                            {!isLoading &&
                                fieldsWithDifferencesMap.get(fieldKey) && (
                                    <UniversalFormField
                                        formId={COMPARE_CONTROL_FIELDS_FORM_ID}
                                        name={fieldKey}
                                        data-id={`${COMPARE_CONTROL_FIELDS_FORM_ID}-field-${fieldKey}-data-id`}
                                    />
                                )}
                        </Box>
                    </React.Fragment>
                );
            })}
        </Grid>
    );
});
