import { useMemo } from 'react';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedCompareControlFieldsModalModel } from '@models/controls';
import { FormWrapper, useFormSubmit } from '@ui/forms';
import { COMPARE_CONTROL_FIELDS_FORM_ID } from '../../constants/compare-control-fields-modal.constants';
import { CompareControlFieldsTable } from './compare-control-fields-table';

export const CompareControlFieldsModal = observer((): React.JSX.Element => {
    const {
        isLoading,
        modalTitle,
        hasDefaultState,
        emptyStateNode,
        modalParagraph,
        closeModal,
        getFooterActions,
        buildSchema,
        handleResetDefaultsControlFieldsValues,
    } = sharedCompareControlFieldsModalModel;

    const { formRef, triggerSubmit } = useFormSubmit();

    const formSchema = useMemo(
        () => (isLoading ? {} : buildSchema()),
        [isLoading, buildSchema],
    );

    const footerActions = useMemo(() => {
        return getFooterActions(triggerSubmit);
    }, [triggerSubmit]);

    return (
        <>
            <Modal.Header
                title={modalTitle}
                closeButtonAriaLabel={t`Close modal`}
                onClose={closeModal}
            />

            <Modal.Body>
                {hasDefaultState && emptyStateNode}
                {!hasDefaultState && (
                    <FormWrapper
                        data-id={`${COMPARE_CONTROL_FIELDS_FORM_ID}-data-id`}
                        formId={COMPARE_CONTROL_FIELDS_FORM_ID}
                        schema={formSchema}
                        ref={formRef}
                        onSubmit={handleResetDefaultsControlFieldsValues}
                    >
                        <Stack direction="column" gap="xl">
                            {modalParagraph}
                            <CompareControlFieldsTable />
                        </Stack>
                    </FormWrapper>
                )}
            </Modal.Body>
            {!isLoading && <Modal.Footer rightActionStack={footerActions} />}
        </>
    );
});
