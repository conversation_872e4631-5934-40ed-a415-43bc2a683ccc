import { isEqual, isNil } from 'lodash-es';
import {
    sharedControlLinkedWorkspacesController,
    sharedControlsExternalEvidenceMutationController,
    sharedControlsUnmapPolicyMutationController,
    sharedLinkedWorkspacesByEvidenceController,
    sharedLinkedWorkspacesByPolicyController,
} from '@controllers/controls';
import type { ButtonProps } from '@cosmos/components/button';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { action, makeAutoObservable, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import type { FormSchema, FormValues } from '@ui/forms';
import { LinkedWorkspacesComboboxField } from '../components/linked-workspaces-combobox-field.component';
import { closeUnmapObjectFromControlModal } from '../helpers/close-unmap-object-modal.helper';
import type { UnmapObjectFromControlModalProps } from '../types/unmap-object-from-control-modal-props.type';

interface UnmapObjectFromControlFormValues {
    linkedWorkspaces?: ListBoxItemData[];
}
class UnmapObjectFromControlModel {
    currentValue: ListBoxItemData[] = [];
    triggerSubmit: (() => Promise<boolean>) | undefined;
    props: Partial<UnmapObjectFromControlModalProps> = {
        controlId: undefined,
        objectId: undefined,
        objectType: undefined,
    };

    constructor() {
        makeAutoObservable(this);
    }

    get objectTypeLabel(): string {
        if (this.props.objectType === 'EVIDENCE') {
            return t`evidence`;
        }

        return t`policy`;
    }

    get hasObjectMultipleLinkedWorkspaces(): boolean {
        return this.controlLinkedWorkspacesOptions.length > 1;
    }

    get modalTitle(): string {
        const { objectTypeLabel, hasObjectMultipleLinkedWorkspaces } = this;

        if (hasObjectMultipleLinkedWorkspaces) {
            return t`Select which workspaces you'd like to unmap for this ${objectTypeLabel}`;
        }

        return t`Unmap ${objectTypeLabel}?`;
    }

    get hasControlLinkedWorkspacesGroup() {
        const { controlLinkedWorkspacesGroup } =
            sharedControlLinkedWorkspacesController;

        return !isNil(controlLinkedWorkspacesGroup?.id);
    }

    get controlLinkedWorkspacesOptions(): ListBoxItemData[] {
        const { objectType } = this.props;

        if (objectType === 'EVIDENCE') {
            return sharedLinkedWorkspacesByEvidenceController.evidenceLinkedWorkspacesOptions;
        }

        return sharedLinkedWorkspacesByPolicyController.policyLinkedWorkspacesOptions;
    }

    get initialValue(): ListBoxItemData[] {
        const { currentWorkspace } = sharedWorkspacesController;

        if (!currentWorkspace) {
            return [];
        }

        return [
            {
                id: String(currentWorkspace.id),
                label: currentWorkspace.name,
            },
        ];
    }

    get schema(): FormSchema {
        return {
            linkedWorkspaces: {
                type: 'custom',
                render: LinkedWorkspacesComboboxField,
                validateWithDefault: 'combobox',
                label: t`Linked workspaces`,
                loaderLabel: t`Loading results`,
                removeAllSelectedItemsLabel: t`Clear all`,
                getSearchEmptyState: () => t`No workspaces found`,
                isMultiSelect: true,
                options: this.controlLinkedWorkspacesOptions,
                placeholder: t`Search by name...`,
                isOptional: true,
                initialValue: this.initialValue,
            },
        };
    }

    get rightActionStack(): ButtonProps[] {
        return [
            {
                label: t`Cancel`,
                level: 'secondary',
                cosmosUseWithCaution_isDisabled: this.isUnmapping,
                a11yLoadingLabel: t`Unmapping...`,
                onClick: this.handleCancel,
            },
            {
                label: t`Unmap`,
                level: 'primary',
                colorScheme: 'danger',
                type: 'submit',
                cosmosUseWithCaution_isDisabled:
                    this.isLoadingObjectLinkedWorkspaces,
                isLoading: this.isUnmapping,
                onClick: action(() => {
                    if (this.hasObjectMultipleLinkedWorkspaces) {
                        this.triggerSubmit?.().catch(() => {
                            logger.error('Failed to submit form');
                        });

                        return;
                    }
                    this.handleSubmit();
                }),
            },
        ];
    }

    handleSubmit = (values?: FormValues): void => {
        const { objectId, controlId, objectType } = this.props;

        if (!controlId || !objectId || !objectType) {
            logger.warn('Missing required props to unmap object from control');

            return;
        }

        const typedValues = values as unknown as
            | UnmapObjectFromControlFormValues
            | undefined;

        const { linkedWorkspaces } = typedValues ?? {};

        const baseBody = this.hasObjectMultipleLinkedWorkspaces
            ? {
                  linkedWorkspaceIds:
                      linkedWorkspaces?.map((item) => Number(item.id)) ?? [],
              }
            : {};

        if (objectType === 'EVIDENCE') {
            sharedControlsExternalEvidenceMutationController.unmapControlEvidence(
                {
                    controlId,
                    libraryEvidenceBody: {
                        ...baseBody,
                        reportIds: [objectId],
                    },
                },
            );
        }

        if (objectType === 'POLICY') {
            sharedControlsUnmapPolicyMutationController.unmapPolicyFromControl(
                controlId,
                {
                    ...baseBody,
                    policyIds: [objectId],
                },
            );
        }

        when(
            () => !this.isUnmapping,
            () => {
                if (this.hasUnmapError) {
                    return;
                }

                closeUnmapObjectFromControlModal();
            },
        );
    };

    handleCancel = (): void => {
        if (
            isEqual(this.currentValue, this.initialValue) ||
            !this.hasObjectMultipleLinkedWorkspaces
        ) {
            closeUnmapObjectFromControlModal();

            return;
        }

        openConfirmationModal({
            title: t`Discard changes?`,
            body: t`Changes will not be saved.`,
            confirmText: t`Ok`,
            cancelText: t`Cancel`,
            type: 'primary',
            onConfirm: () => {
                closeConfirmationModal();
                closeUnmapObjectFromControlModal();
            },
            onCancel: closeConfirmationModal,
        });
    };

    get isUnmapping(): boolean {
        return (
            sharedControlsExternalEvidenceMutationController.isUnmapPending ||
            sharedControlsUnmapPolicyMutationController.isUnmapping
        );
    }

    get hasUnmapError(): boolean {
        return (
            sharedControlsExternalEvidenceMutationController.hasUnmapError ||
            sharedControlsUnmapPolicyMutationController.hasError
        );
    }

    get isLoadingObjectLinkedWorkspaces(): boolean {
        return (
            sharedLinkedWorkspacesByEvidenceController.isLoading ||
            sharedLinkedWorkspacesByPolicyController.isLoading
        );
    }

    load = (): void => {
        const { objectId, objectType } = this.props;
        const { controlLinkedWorkspacesGroup } =
            sharedControlLinkedWorkspacesController;

        if (!controlLinkedWorkspacesGroup?.id || !objectId || !objectType) {
            logger.warn('Missing required props to unmap object from control');

            return;
        }

        if (objectType === 'EVIDENCE') {
            sharedLinkedWorkspacesByEvidenceController.load(
                objectId,
                controlLinkedWorkspacesGroup.id,
            );
        }

        if (objectType === 'POLICY') {
            sharedLinkedWorkspacesByPolicyController.load(
                objectId,
                controlLinkedWorkspacesGroup.id,
            );
        }
    };
}

export const unmapObjectFromControlModel = new UnmapObjectFromControlModel();
