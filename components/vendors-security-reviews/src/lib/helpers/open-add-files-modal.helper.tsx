import { modalController } from '@controllers/modal';
import {
    sharedVendorsDetailsController,
    sharedVendorsSecurityReviewDocumentsController,
} from '@controllers/vendors';
import { action } from '@globals/mobx';
import { AddFilesModal } from '../components/vendors-add-files-modal';
import { ADD_FILES_MODAL_ID } from '../constants/add-files-modal.constants';

export const openAddFilesModal = action((): void => {
    // Ensure vendor ID is set in the documents controller before opening modal
    if (!sharedVendorsSecurityReviewDocumentsController.currentVendorId) {
        const vendorId = sharedVendorsDetailsController.vendorDetails?.id;

        if (vendorId) {
            sharedVendorsSecurityReviewDocumentsController.setVendorId(
                vendorId,
            );
        }
    }

    modalController.openModal({
        id: ADD_FILES_MODAL_ID,
        content: () => <AddFilesModal data-id="add-files-modal" />,
        centered: true,
        disableClickOutsideToClose: false,
        size: 'lg',
    });
});

export const handleAddFilesCloseModal = (): void => {
    modalController.closeModal(ADD_FILES_MODAL_ID);
};
