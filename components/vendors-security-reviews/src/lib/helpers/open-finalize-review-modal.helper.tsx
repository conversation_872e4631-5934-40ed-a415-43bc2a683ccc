import { modalController } from '@controllers/modal';
import { action } from '@globals/mobx';
import { FinalizeReviewModal } from '../components/vendors-finalize-review-modal';
import { FINALIZE_REVIEW_MODAL_ID } from '../constants/finalize-review-modal.constants';

export const openFinalizeReviewModal = action((): void => {
    modalController.openModal({
        id: FINALIZE_REVIEW_MODAL_ID,
        content: () => <FinalizeReviewModal data-id="finalize-review-modal" />,
        centered: true,
        disableClickOutsideToClose: false,
        size: 'md',
    });
});

export const handleFinalizeReviewCloseModal = (): void => {
    modalController.closeModal(FINALIZE_REVIEW_MODAL_ID);
};
