import { t } from '@globals/i18n/macro';

export const FINALIZE_REVIEW_MODAL_ID = 'finalize-review-modal';
export const FINALIZE_REVIEW_FORM_ID = 'finalize-review-form';

export const VENDOR_SECURITY_REVIEW_DECISION_STATUS = {
    PENDING: 'PENDING',
    APPROVED: 'APPROVED',
    APPROVED_WITH_CONDITIONS: 'APPROVED_WITH_CONDITIONS',
    REJECTED: 'REJECTED',
} as const;

export const getDecisionLabel = (
    decision: keyof typeof VENDOR_SECURITY_REVIEW_DECISION_STATUS,
): string => {
    switch (decision) {
        case 'PENDING': {
            return t`Pending`;
        }
        case 'APPROVED': {
            return t`Approved`;
        }
        case 'APPROVED_WITH_CONDITIONS': {
            return t`Approved with conditions`;
        }
        case 'REJECTED': {
            return t`Rejected`;
        }
        default: {
            return t`Unknown`;
        }
    }
};

export const getDecisionOptions = (): {
    id: string;
    label: string;
    value: string;
}[] => {
    return Object.entries(VENDOR_SECURITY_REVIEW_DECISION_STATUS).map(
        ([key, value]) => ({
            id: value,
            label: getDecisionLabel(
                key as keyof typeof VENDOR_SECURITY_REVIEW_DECISION_STATUS,
            ),
            value,
        }),
    );
};
