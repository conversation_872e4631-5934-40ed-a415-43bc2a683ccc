import { useState } from 'react';
import {
    sharedVendorsSecurityReviewDetailsController,
    sharedVendorsSecurityReviewDocumentsController,
} from '@controllers/vendors';
import { Modal } from '@cosmos/components/modal';
import { RadioFieldGroup } from '@cosmos/components/radio-field-group';
import { Stack } from '@cosmos/components/stack';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { observer, runInAction } from '@globals/mobx';
import { type FormValues, useFormSubmit } from '@ui/forms';
import { handleAddFilesCloseModal } from '../helpers/open-add-files-modal.helper';
import { VendorsAddFilesFromComputer } from './vendors-add-files-from-computer';
import { VendorsAddFilesReportsAndDocument } from './vendors-add-files-reports-and-document';

export const AddFilesModal = observer((): React.JSX.Element => {
    const { formRef: formDocumentsRef, triggerSubmit: triggerSubmitDocuments } =
        useFormSubmit();
    const { formRef: formUploadRef, triggerSubmit: triggerSubmitUpload } =
        useFormSubmit();

    const { isAddingFiles } = sharedVendorsSecurityReviewDocumentsController;

    const { securityReviewDetails } =
        sharedVendorsSecurityReviewDetailsController;

    const { isVendorEditable } = sharedFeatureAccessModel;

    const [fileSource, setFileSource] = useState('documents');

    // Check permissions
    if (!isVendorEditable) {
        return (
            <>
                <Modal.Header
                    closeButtonAriaLabel={t`Close`}
                    title={t`Add files`}
                    onClose={handleAddFilesCloseModal}
                />
                <Modal.Body>
                    <Stack gap="xl" direction="column">
                        {t`You don't have permission to add files to security reviews.`}
                    </Stack>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Close`,
                            level: 'secondary',
                            onClick: handleAddFilesCloseModal,
                        },
                    ]}
                />
            </>
        );
    }

    const handleSubmitDocuments = (values: FormValues): void => {
        const document = values.report as FormValues;
        const securityReviewId = securityReviewDetails?.id;

        if (!securityReviewId) {
            logger.error({
                message: 'No security review ID found',
                additionalInfo: {
                    securityReviewDetails,
                },
            });

            return;
        }

        runInAction(() => {
            sharedVendorsSecurityReviewDocumentsController.addDocumentFromLibrary(
                Number(document.id),
                securityReviewId,
                handleAddFilesCloseModal,
                () => {
                    logger.error({
                        message: 'Failed to add document from library',
                        additionalInfo: {
                            securityReviewId,
                            documentId: document.id,
                        },
                    });
                },
            );
        });
    };

    const handleSubmitUpload = (values: FormValues): void => {
        const files = values.fileUpload as File[];
        const securityReviewId = securityReviewDetails?.id;

        if (!securityReviewId) {
            logger.error({
                message: 'Missing security review ID',
                additionalInfo: {
                    securityReviewDetails,
                },
            });

            return;
        }

        runInAction(() => {
            // Use uploadFilesFromComputer for all cases - handles both single and multiple files
            sharedVendorsSecurityReviewDocumentsController.uploadFilesFromComputer(
                files,
                securityReviewId,
                handleAddFilesCloseModal,
                () => {
                    logger.error({
                        message:
                            'Failed to upload files and link to security review',
                        additionalInfo: {
                            securityReviewId,
                            files,
                        },
                    });
                },
            );
        });
    };

    return (
        <>
            <Modal.Header
                closeButtonAriaLabel={t`Close`}
                title={t`Add files`}
                description={t`Upload files you'd like to include in this review.`}
                onClose={handleAddFilesCloseModal}
            />
            <Modal.Body>
                <Stack gap="xl" direction="column">
                    <RadioFieldGroup
                        formId="add-files-modal"
                        label={t`Select the source of your file`}
                        name="file-source"
                        value={fileSource}
                        cosmosUseWithCaution_forceOptionOrientation="vertical"
                        options={[
                            {
                                label: t`Reports and documents`,
                                value: 'documents',
                            },
                            {
                                label: t`Upload from computer`,
                                value: 'upload',
                            },
                        ]}
                        onChange={() => {
                            fileSource === 'documents'
                                ? setFileSource('upload')
                                : setFileSource('documents');
                        }}
                    />
                    {fileSource === 'documents' ? (
                        <VendorsAddFilesReportsAndDocument
                            formId="add-files-modal"
                            formRef={formDocumentsRef}
                            onSubmit={handleSubmitDocuments}
                        />
                    ) : (
                        <VendorsAddFilesFromComputer
                            formId="upload-files-modal"
                            formRef={formUploadRef}
                            onSubmit={handleSubmitUpload}
                        />
                    )}
                </Stack>
            </Modal.Body>

            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Close`,
                        level: 'secondary',
                        cosmosUseWithCaution_isDisabled: isAddingFiles,
                        onClick: handleAddFilesCloseModal,
                    },
                    {
                        label: t`Confirm`,
                        level: 'primary',
                        colorScheme: 'primary',
                        type: 'submit',
                        isLoading: isAddingFiles,
                        onClick: () => {
                            if (fileSource === 'documents') {
                                triggerSubmitDocuments().catch(() => {
                                    logger.error({
                                        message: 'Failed to submit form',
                                        additionalInfo: {
                                            fileSource,
                                        },
                                    });
                                });
                            } else {
                                triggerSubmitUpload().catch(() => {
                                    logger.error({
                                        message: 'Failed to submit form',
                                        additionalInfo: {
                                            fileSource,
                                        },
                                    });
                                });
                            }
                        },
                    },
                ]}
            />
        </>
    );
});
