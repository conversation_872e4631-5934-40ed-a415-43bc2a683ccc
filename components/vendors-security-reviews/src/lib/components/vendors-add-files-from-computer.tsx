import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { Form, type FormValues } from '@ui/forms';
import {
    getUploadFileErrorMessages,
    VENDORS_ADD_SOC_REPORT_FROM_COMPUTER_FILES_ACCEPTED_FORMATS,
} from '@views/vendors-add-soc-report-modal';

interface VendorsAddFilesFromComputerProps {
    formId: string;
    formRef: React.RefObject<HTMLFormElement>;
    onSubmit: (values: FormValues) => void;
}

export const VendorsAddFilesFromComputer = observer(
    ({
        formId,
        formRef,
        onSubmit,
    }: VendorsAddFilesFromComputerProps): React.JSX.Element => {
        return (
            <Form
                hasExternalSubmitButton
                formId={formId}
                data-id="vendors-add-files-from-computer-form"
                data-testid="vendors-add-files-from-computer-form"
                ref={formRef}
                schema={{
                    fileUpload: {
                        type: 'file',
                        label: t`Files`,
                        errorCodeMessages: getUploadFileErrorMessages(),
                        innerLabel: t`Or drop files here`,
                        isMulti: true,
                        oneFileOnly: false,
                        removeButtonText: t`Remove file`,
                        selectButtonText: t`Upload files`,
                        acceptedFormats:
                            VENDORS_ADD_SOC_REPORT_FROM_COMPUTER_FILES_ACCEPTED_FORMATS,
                    },
                }}
                onSubmit={onSubmit}
            />
        );
    },
);
