import { sharedVendorsProfileReportsAndDocumentsController } from '@controllers/vendors';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { Form, type FormValues } from '@ui/forms';

interface VendorsAddFilesReportsAndDocumentProps {
    formId: string;
    formRef: React.RefObject<HTMLFormElement>;
    onSubmit: (values: FormValues) => void;
}

export const VendorsAddFilesReportsAndDocument = observer(
    ({
        formId,
        formRef,
        onSubmit,
    }: VendorsAddFilesReportsAndDocumentProps): React.JSX.Element => {
        const { options, hasNextPage, isFetching, onFetchDocuments } =
            sharedVendorsProfileReportsAndDocumentsController;

        return (
            <Form
                hasExternalSubmitButton
                formId={formId}
                data-id="vendors-add-files-reports-and-document-form"
                data-testid="vendors-add-files-reports-and-document-form"
                ref={formRef}
                schema={{
                    report: {
                        type: 'combobox',
                        options,
                        label: t`Select report`,
                        loaderLabel: t`Loading reports`,
                        getSearchEmptyState: () => t`No reports found`,
                        clearSelectedItemButtonLabel: t`Clear report`,
                        isLoading: isFetching,
                        hasMore: hasNextPage,
                        onFetchOptions: ({ search, increasePage }) => {
                            onFetchDocuments({
                                search,
                                increasePage,
                            });
                        },
                    },
                }}
                onSubmit={onSubmit}
            />
        );
    },
);
