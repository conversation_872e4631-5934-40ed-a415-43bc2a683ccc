import { sharedAuthController } from '@controllers/auth';
import { LogoLoader } from '@cosmos-lab/components/logo-loader';
import type { Regions } from '@globals/config';
import { action, observer } from '@globals/mobx';
import {
    type ClientLoaderFunction,
    type MetaFunction,
    Navigate,
    redirect,
} from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => [{ title: 'Drata' }];

export const clientLoader: ClientLoaderFunction = action(({ request }) => {
    const url = new URL(request.url);
    const token = url.searchParams.get('token');
    const isLegacyFlow = url.searchParams.get('legacy') === 'true';
    const email = url.searchParams.get('email');
    // TODO: default to NA for now
    const region = url.searchParams.get('region') ?? 'NA';

    if (!token) {
        // TODO: use logger
        console.error(
            'Magic link authentication failed: Missing token parameter',
        );

        return redirect('/auth/login?error=invalid_token');
    }

    console.debug({
        token,
        isLegacyFlow,
        email,
        region,
        message: 'Attempting magic link authentication',
    });

    try {
        if (isLegacyFlow) {
            if (!email || !region) {
                // TODO: use logger
                console.error(
                    'Legacy authentication failed: Missing required parameters',
                    {
                        hasEmail: Boolean(email),
                        hasRegion: Boolean(region),
                    },
                );

                return redirect('/auth/login?error=missing_parameters');
            }

            sharedAuthController.finalizeLoginFromLegacy(
                token,
                email,
                region as Regions,
            );
        } else {
            sharedAuthController.updateRegion(region as Regions);
            sharedAuthController.finalizeLogin(token);
        }
    } catch (error) {
        // TODO: use logger
        console.error('Authentication process failed:', error);

        return redirect('/auth/login?error=authentication_failed');
    }

    return null;
});

const MagicLinkUserPage = observer((): React.JSX.Element => {
    const { isUserAuthenticated, isLoggingIn } = sharedAuthController;

    return (
        <RouteLandmark
            as="section"
            data-testid="MagicLinkUserPage"
            data-id="EHqJtpUY"
        >
            <LogoLoader fadeOut={!isLoggingIn} ariaLabel="Logging In" />
            {!isLoggingIn && isUserAuthenticated && (
                <Navigate
                    replace
                    data-testid="Redirect"
                    data-id="zcE3bjjJ"
                    to="/auth/accept-terms"
                />
            )}
        </RouteLandmark>
    );
});

export default MagicLinkUserPage;
