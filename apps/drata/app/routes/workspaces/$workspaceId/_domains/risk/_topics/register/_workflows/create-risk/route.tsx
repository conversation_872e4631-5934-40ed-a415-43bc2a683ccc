import type { <PERSON>lientLoader } from '@app/types';
import {
    RISK_OWNER_ROLES,
    sharedRiskCategoriesController,
    sharedRiskSettingsController,
} from '@controllers/risk';
import { sharedUsersInfiniteController } from '@controllers/users';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { AppLink } from '@ui/app-link';
import { RiskRegisterCreateRiskView } from '@views/risk-register-create-risk';

export const meta: MetaFunction = () => [{ title: 'New risk' }];

export const clientLoader = action((): ClientLoader => {
    sharedUsersInfiniteController.loadUsers({ roles: RISK_OWNER_ROLES });
    sharedRiskCategoriesController.loadCategories();
    sharedRiskSettingsController.load();

    return {
        pageHeader: {
            isCentered: true,
            title: 'Add Risk',
            backLink: (
                <AppLink
                    href="risk/register/library"
                    label="Back to Risk Register"
                    size="sm"
                />
            ),
        },
        contentNav: {
            tabs: [],
        },
    };
});

const RiskRegisterCreateRisk = (): React.JSX.Element => {
    return (
        <RiskRegisterCreateRiskView
            data-testid="RiskRegisterCreateRisk"
            data-id="Svebw83n"
        />
    );
};

export default RiskRegisterCreateRisk;
