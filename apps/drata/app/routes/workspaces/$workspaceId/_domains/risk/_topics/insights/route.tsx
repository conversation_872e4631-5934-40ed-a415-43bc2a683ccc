import type { ClientLoader } from '@app/types';
import type { PageHeaderOverrides } from '@controllers/page-header';
import {
    sharedRiskInsightsController,
    sharedRiskInsightsDownloadController,
    sharedRiskSettingsController,
} from '@controllers/risk';
import { sharedRiskInsightsOverTimeController } from '@controllers/risk-insights-over-time';
import { ActionStack } from '@cosmos/components/action-stack';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { RiskInsightsView } from '@views/risk-insights';

export const meta: MetaFunction = () => {
    return [{ title: t`Risk Insights` }];
};

export class RiskInsightsPageHeaderModel implements PageHeaderOverrides {
    constructor() {
        makeAutoObservable(this);
    }

    pageId = 'risk-insights-page-header';
    isCentered = true;

    get actionStack(): React.JSX.Element {
        const { downloadAll } = sharedRiskInsightsDownloadController;

        return (
            <ActionStack
                data-id="vendors-questionnaires-add-page-action-stack"
                stacks={[
                    {
                        actions: [
                            {
                                actionType: 'button',
                                id: 'risk-insights-download-button',
                                typeProps: {
                                    label: t`Download`,
                                    startIconName: 'Download',
                                    level: 'secondary',
                                    onClick: () => {
                                        downloadAll();
                                    },
                                },
                            },
                        ],
                        id: `risk-insights-actions-stack-0`,
                    },
                ]}
            />
        );
    }

    title = t`Risk Insights`;
}

const headerModel = new RiskInsightsPageHeaderModel();

export const clientLoader = action((): ClientLoader => {
    sharedRiskInsightsController.initialize();
    sharedRiskSettingsController.load();
    sharedRiskInsightsOverTimeController.initialize();

    return {
        pageHeader: {
            title: headerModel.title,
            actionStack: headerModel.actionStack,
        },
    };
});

const RiskInsights = (): React.JSX.Element => {
    return <RiskInsightsView data-testid="RiskInsights" data-id="D61xoR_R" />;
};

export default RiskInsights;
