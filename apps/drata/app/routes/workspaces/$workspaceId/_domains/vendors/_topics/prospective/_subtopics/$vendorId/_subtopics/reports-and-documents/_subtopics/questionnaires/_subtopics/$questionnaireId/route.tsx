import type { ClientLoader } from '@app/types';
import {
    sharedVendorsProfileQuestionnaireAISummaryController,
    sharedVendorsQuestionnairesController,
} from '@controllers/vendors';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { VendorsProfileReportsAndDocumentsQuestionnairePageHeaderModel } from '@models/vendors-profile';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { VendorsSecurityReviewQuestionnaireView } from '@views/vendors-security-review-questionnaire';

export const meta: MetaFunction = () => [
    {
        title: t`Prospective Vendors Profile Reports and documents Questionnaire`,
    },
];

export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { vendorId, questionnaireId } = params;

        if (!vendorId) {
            throw new Error('Vendor ID is required');
        }

        if (!questionnaireId) {
            throw new Error('Questionnaire ID is required');
        }

        sharedVendorsProfileQuestionnaireAISummaryController.loadQuestionnaireSummary(
            Number(questionnaireId),
        );

        sharedVendorsQuestionnairesController.loadAll(
            Number(vendorId),
            Number(questionnaireId),
        );

        return {
            pageHeader:
                new VendorsProfileReportsAndDocumentsQuestionnairePageHeaderModel(),
        };
    },
);

const VendorsProspectiveReportsAndDocumentsQuestionnaires =
    (): React.JSX.Element => {
        return (
            <VendorsSecurityReviewQuestionnaireView
                showActions
                data-testid="VendorsProspectiveReportsAndDocumentsQuestionnaires"
                data-id="P9xKLm8N"
            />
        );
    };

export default VendorsProspectiveReportsAndDocumentsQuestionnaires;
