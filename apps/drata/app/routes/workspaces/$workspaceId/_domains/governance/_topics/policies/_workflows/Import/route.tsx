import type { ClientLoader } from '@app/types';
import { sharedConnectionsController } from '@controllers/connections';
import { sharedPersonnelGroupController } from '@controllers/personnel-group-controller';
import { sharedCreatePolicyController } from '@controllers/policies';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { PoliciesAddView } from '@views/policies-add';

export const meta: MetaFunction = () => [{ title: 'Import Policy' }];

export const clientLoader = action((): ClientLoader => {
    sharedConnectionsController.allConfiguredConnectionsQuery.load();
    sharedPersonnelGroupController.loadPersonnelGroups();
    sharedCreatePolicyController.setExternal(true);

    return {
        pageHeader: {
            title: t`Import Policy`,
            pageId: 'policies-import',
            isCentered: true,
        },
        tabs: [],
    };
});

const ImportPolicy = (): React.JSX.Element => {
    return (
        <PoliciesAddView
            isExternal
            data-testid="ImportPolicy"
            data-id="RPUzAeUB"
        />
    );
};

export default ImportPolicy;
