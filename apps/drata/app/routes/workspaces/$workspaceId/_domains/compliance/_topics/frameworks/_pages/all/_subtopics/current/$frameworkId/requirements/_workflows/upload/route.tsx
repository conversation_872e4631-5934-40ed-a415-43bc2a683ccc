import type { ClientLoader } from '@app/types';
import { FrameworkUploadRequirementPageHeaderModel } from '@models/framework-create';
import { FrameworkRequirementsUploadView } from '@views/framework-requirements-upload';

export const clientLoader = (): ClientLoader => {
    return {
        pageHeader: new FrameworkUploadRequirementPageHeaderModel(),
    };
};

const FrameworkRequirementUpload = (): React.JSX.Element => {
    return (
        <FrameworkRequirementsUploadView
            data-testid="FrameworkRequirementUpload"
            data-id="rkJ1-kbQ"
        />
    );
};

export default FrameworkRequirementUpload;
