import type { <PERSON>lient<PERSON>oader } from '@app/types';
import { sharedConnectionsController } from '@controllers/connections';
import {
    sharedControlInfiniteAllOwnersController,
    sharedControlsController,
} from '@controllers/controls';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { ControlsPageHeaderModel } from '@models/controls';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => [{ title: t`Controls` }];

export const clientLoader = action((): ClientLoader => {
    const { hasReadControlPermission } = sharedFeatureAccessModel;

    if (!hasReadControlPermission) {
        throw new Error('Missing permission to access Controls', {
            cause: '403',
        });
    }

    sharedControlsController.load({
        isArchived: false,
    });
    sharedConnectionsController.allConfiguredConnectionsQuery.load();
    sharedControlInfiniteAllOwnersController.loadControlOwners();

    return {
        pageHeader: new ControlsPageHeaderModel(),
    };
});

const Controls = (): React.JSX.Element => {
    return (
        <RouteLandmark as="section" data-testid="Controls" data-id="jPwZmxJb">
            <Outlet />
        </RouteLandmark>
    );
};

export default Controls;
