import { useEffect } from 'react';
import type { ClientLoader } from '@app/types';
import { sharedFrameworkCreateController } from '@controllers/frameworks';
import { t } from '@globals/i18n/macro';
import { action, reaction } from '@globals/mobx';
import { getParentRoute } from '@helpers/path';
import { FrameworkCreatePageHeaderModel } from '@models/framework-create';
import type { MetaFunction } from '@remix-run/node';
import { useLocation, useNavigate } from '@remix-run/react';
import { CreateFrameworkView } from '@views/create-framework';

export const meta: MetaFunction = () => {
    return [{ title: t`Create custom framework` }];
};
export const clientLoader = action((): ClientLoader => {
    sharedFrameworkCreateController.load();

    return {
        pageHeader: new FrameworkCreatePageHeaderModel(),
    };
});

const CreateFramework = (): React.JSX.Element => {
    const location = useLocation();
    const { pathname } = location;
    const parentRoute = getParentRoute(pathname);
    const navigate = useNavigate();

    useEffect(() => {
        const disposer = reaction(
            () => sharedFrameworkCreateController.hasReachedLimit,
            (hasReachedLimit) => {
                if (hasReachedLimit) {
                    navigate(parentRoute);
                }
            },
        );

        return () => {
            disposer();
        };
    }, [navigate, parentRoute]);

    return (
        <CreateFrameworkView data-testid="CreateFramework" data-id="COUgDp03" />
    );
};

export default CreateFramework;
