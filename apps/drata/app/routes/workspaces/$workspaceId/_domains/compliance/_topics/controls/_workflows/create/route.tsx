import type { <PERSON>lient<PERSON>oader } from '@app/types';
import { sharedControlsController } from '@controllers/controls-owners-candidates';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunctionArgs } from '@remix-run/react';
import { AppLink } from '@ui/app-link';
import { CreateControlInfoView } from '@views/create-control-info';

export const meta: MetaFunction = () => {
    return [{ title: t`Create control` }];
};

export const clientLoader = action(
    ({ request }: ClientLoaderFunctionArgs): ClientLoader => {
        const { hasCreateControlPermission } = sharedFeatureAccessModel;

        if (!hasCreateControlPermission) {
            throw new Error('Missing permission to create Controls', {
                cause: '403',
            });
        }

        sharedControlsController.loadControlOwnersCandidates({
            page: 1,
        });

        const parentHref = new URL(
            request.url.split('/').slice(0, -1).join('/'),
        ).pathname;

        return {
            pageHeader: {
                title: t`Create control`,
                backLink: (
                    <AppLink href={parentHref} data-testid="BackLink" size="sm">
                        {t`Back to Controls`}
                    </AppLink>
                ),
                isCentered: true,
            },
            topicsNav: {},
            tabs: [],
        };
    },
);

const CreateControl = (): React.JSX.Element => {
    return (
        <CreateControlInfoView data-testid="CreateControl" data-id="bzQBvtVm" />
    );
};

export default CreateControl;
