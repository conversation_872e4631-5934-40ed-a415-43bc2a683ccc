import { isEmpty } from 'lodash-es';
import { sharedConnectionsController } from '@controllers/connections';
import { sharedEventsController } from '@controllers/events';
import { sharedMonitoringSummariesController } from '@controllers/monitoring';
import {
    activeTicketsMetadataController,
    activeTrackCardController,
} from '@controllers/monitoring-details';
import { sharedWorkspaceMonitorsController } from '@controllers/workspace-monitors';
import { DEFAULT_PARAMS } from '@cosmos/components/datatable';
import { action, when } from '@globals/mobx';
import type { ClientLoaderFunctionArgs } from '@remix-run/react';
import { MonitoringDetailsOverviewView } from '@views/monitoring-details-overview';

export const clientLoader = action(
    ({ params }: ClientLoaderFunctionArgs): null => {
        const { monitorId, workspaceId } = params;
        const testId = Number(monitorId);

        sharedConnectionsController.allConfiguredConnectionsQuery.load();
        // Load events with specific parameters for this test
        sharedEventsController.loadEvents({
            ...DEFAULT_PARAMS,
            additionalQuery: {
                source: 'AUTOPILOT',
                category: 'AUTOPILOT',
                testId,
                workspaceId,
                mostRecent: true,
                paginated: true,
            },
        });
        activeTicketsMetadataController.loadTicketsMetadata(testId);
        activeTrackCardController.loadTrackData(testId);
        sharedWorkspaceMonitorsController.loadWorkspaceMonitorTestOverview(
            testId,
        );

        // Wait for events data to load, then check for existing execution groups
        when(
            () => !isEmpty(sharedEventsController.events),
            () => {
                // Load existing execution groups with failed feature IDs
                sharedMonitoringSummariesController.checkExistingExecutions(
                    testId,
                    sharedEventsController.failedFeatureIds,
                );
            },
        );

        return null;
    },
);

const MonitoringDetailsOverview = (): React.JSX.Element => {
    return (
        <MonitoringDetailsOverviewView
            data-testid="MonitoringDetailsOverview"
            data-id="monitoring-details-overview"
        />
    );
};

export default MonitoringDetailsOverview;
