import type { ClientLoader } from '@app/types';
import { sharedRequirementCreateController } from '@controllers/requirements';
import { FrameworkCreateRequirementPageHeaderModel } from '@models/framework-create';
import type { LoaderFunctionArgs } from '@remix-run/node';
import { CreateFrameworkRequirementView } from '@views/create-framework-requirement';

export const clientLoader = ({ params }: LoaderFunctionArgs): ClientLoader => {
    const { frameworkId } = params;

    if (!frameworkId) {
        throw new Error('Framework ID is required');
    }

    sharedRequirementCreateController.setFrameworkId(Number(frameworkId));
    sharedRequirementCreateController.loadRequirementCategories(
        Number(frameworkId),
    );

    return {
        pageHeader: new FrameworkCreateRequirementPageHeaderModel(),
    };
};

const CreateFrameworkRequirement = (): React.JSX.Element => {
    return (
        <CreateFrameworkRequirementView
            data-testid="CreateFrameworkRequirement"
            data-id="XwTLzmhc"
        />
    );
};

export default CreateFrameworkRequirement;
