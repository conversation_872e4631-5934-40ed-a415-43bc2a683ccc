import { isEmpty, isNil } from 'lodash-es';
import { getPolicyStatusLabel } from '@components/policies';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { Grid } from '@cosmos/components/grid';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Metadata } from '@cosmos/components/metadata';
import { PanelBody } from '@cosmos/components/panel';
import { TagGroup } from '@cosmos/components/tag-group';
import { Text } from '@cosmos/components/text';
import { observer } from '@globals/mobx';
import { formatDate } from '@helpers/date-time';
import { sharedPolicyDetailsModel } from '@models/policies';
import { formatUserValue } from '../helpers/format-user-value.helper';

export const PolicyDetailsBodyPanelComponent = observer(
    (): React.JSX.Element => {
        const {
            policy: policyDetails,
            policyControlsAssociated: linkedControls,
            policyFrameworksAssociated: frameworks,
            isPolicyLoading,
            policyControlsAssociatedQuery: {
                isLoading: isPolicyControlsAssociatedLoading,
            },
            policyFrameworksAssociatedQuery: {
                isLoading: isPolicyFrameworksAssociatedLoading,
            },
        } = sharedPolicyBuilderController;
        const {
            versionStatus,
            renewalDate,
            currentOwner,
            description,
            shouldDisplaySLA,
            shouldShowLinkedWorkspaces,
            policyLinkedWorkspaces,
        } = sharedPolicyDetailsModel;

        return (
            <PanelBody
                data-testid="PolicyDetailsBodyPanelComponent"
                data-id="o_ssD_o"
            >
                <Grid gap="4x">
                    <Text type="title" size="400">
                        Overview
                    </Text>
                    <KeyValuePair
                        showEmptyValue={isNil(versionStatus)}
                        isLoading={isPolicyLoading}
                        type="BADGE"
                        label="Status"
                        value={getPolicyStatusLabel(versionStatus)}
                    />

                    <KeyValuePair
                        showEmptyValue={isNil(renewalDate)}
                        isLoading={isPolicyLoading}
                        type="TEXT"
                        label="Renewal date"
                        value={formatDate('field', renewalDate)}
                    />

                    {currentOwner && !isPolicyLoading && (
                        <KeyValuePair
                            showEmptyValue={isNil(currentOwner)}
                            isLoading={isPolicyLoading}
                            label="Owner"
                            type="USER"
                            value={formatUserValue(currentOwner)}
                        />
                    )}

                    <KeyValuePair
                        isLoading={isPolicyLoading}
                        type="TEXT"
                        label="Description"
                        value={description}
                    />

                    {shouldShowLinkedWorkspaces && (
                        <KeyValuePair
                            showEmptyValue={isEmpty(policyLinkedWorkspaces)}
                            isLoading={isPolicyLoading}
                            type="REACT_NODE"
                            label="Linked workspaces"
                            value={
                                <TagGroup
                                    data-id="policy-linked-workspaces-tag-group"
                                    maxVisibleTags={2}
                                >
                                    {policyLinkedWorkspaces.map((workspace) => (
                                        <Metadata
                                            key={workspace.workspace.id}
                                            label={workspace.workspace.name}
                                            colorScheme="neutral"
                                            type="tag"
                                            data-id={`policy-linked-workspace-${workspace.workspace.id}`}
                                        />
                                    ))}
                                </TagGroup>
                            }
                        />
                    )}

                    <KeyValuePair
                        showEmptyValue={isEmpty(linkedControls)}
                        isLoading={isPolicyControlsAssociatedLoading}
                        label="Linked controls"
                        type="REACT_NODE"
                        value={
                            <TagGroup
                                data-id="policy-linked-controls-tag-group"
                                maxVisibleTags={2}
                            >
                                {linkedControls.map((control) => (
                                    <Metadata
                                        key={control.id}
                                        label={control.code}
                                        colorScheme="neutral"
                                        type="tag"
                                        data-id={`policy-linked-control-${control.id}`}
                                    />
                                ))}
                            </TagGroup>
                        }
                    />

                    <KeyValuePair
                        showEmptyValue={isEmpty(frameworks)}
                        isLoading={isPolicyFrameworksAssociatedLoading}
                        label="Frameworks"
                        type="REACT_NODE"
                        value={
                            <TagGroup
                                data-id="policy-frameworks-tag-group"
                                maxVisibleTags={2}
                            >
                                {frameworks.map((framework) => (
                                    <Metadata
                                        key={framework.id}
                                        label={framework.pill}
                                        colorScheme="neutral"
                                        type="tag"
                                        data-id={`policy-framework-${framework.id}`}
                                    />
                                ))}
                            </TagGroup>
                        }
                    />

                    <KeyValuePair
                        isLoading={isPolicyLoading}
                        type="TEXT"
                        label="Policies replaced"
                        value={String(
                            policyDetails?.replacedPolicies.length ?? 0,
                        )}
                    />

                    {shouldDisplaySLA && (
                        <KeyValuePair
                            isLoading={isPolicyLoading}
                            type="TEXT"
                            label="SLA"
                            value={'Monitored by Drata'}
                        />
                    )}
                </Grid>
            </PanelBody>
        );
    },
);
