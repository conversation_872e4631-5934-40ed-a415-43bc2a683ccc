import { type AllowedBackgroundToken, Box } from '@cosmos/components/box';

export interface ColoredSquareProps {
    color?: string;
}

export const ColoredSquare = ({
    color,
}: ColoredSquareProps): React.JSX.Element => {
    return (
        <Box
            borderRadius="borderRadiusMd"
            width="5xl"
            height="5xl"
            data-testid="ColoredSquare"
            data-id="oCX4SGKJ"
            backgroundColor={
                color as Extract<AllowedBackgroundToken, `dataDiverge${string}`>
            }
        />
    );
};
