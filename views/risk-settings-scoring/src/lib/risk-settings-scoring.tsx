import type React from 'react';
import { createNumericOptions } from '@controllers/risk';
import { sharedRiskSettingsController } from '@controllers/risk-settings';
import { Accordion } from '@cosmos/components/accordion';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { EmptyState } from '@cosmos/components/empty-state';
import { Grid } from '@cosmos/components/grid';
import { Icon } from '@cosmos/components/icon';
import { SelectField } from '@cosmos/components/select-field';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { TextField } from '@cosmos/components/text-field';
import { RiskScore } from '@cosmos-lab/components/risk-score';
import { Threshold } from '@cosmos-lab/components/threshold';
import { Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getScoreIntensityByThresholds } from '@helpers/risk-score';
import { Form } from '@ui/forms';
import { ThresholdSettingsItem } from './threshold-settings-item';

/**
 *All the todos in this page are going to be work on https://drata.atlassian.net/browse/ENG-73374.
 */
const handleSave = () => {
    //TODO: Implement save logic
};

export const RiskSettingsScoring = observer((): React.JSX.Element => {
    const { isLoading, riskSettings } = sharedRiskSettingsController;

    if (isLoading) {
        return <Skeleton barCount={10} />;
    }

    if (!riskSettings) {
        return (
            <EmptyState
                title="No Risk Settings Available"
                description="Risk settings could not be loaded. Please try refreshing the page."
            />
        );
    }

    const impactOptions = createNumericOptions(riskSettings.impact);
    const likelihoodOptions = createNumericOptions(riskSettings.likelihood);
    // TODO: Implement impact change riskScore model
    const baseRiskScore = riskSettings.impact * riskSettings.likelihood;

    const riskScore = baseRiskScore;

    const severity = getScoreIntensityByThresholds(
        riskScore,
        riskSettings.thresholds,
    );

    return (
        <Form
            hasExternalSubmitButton
            formId="risk-settings-scoring"
            data-id="vpMlcfzS"
            schema={{
                riskSettingsContent: {
                    type: 'custom',
                    label: 'Risk Settings Configuration',
                    render: () => (
                        <Stack
                            direction="column"
                            gap="2xl"
                            data-testid="RiskSettingsScoring"
                            data-id="pj5RFoRL"
                            width="100%"
                        >
                            <Text size="300" type="title">
                                <Trans>Impact & Likelihood</Trans>
                            </Text>
                            <Text size="300" type="body">
                                <Trans>
                                    You can adjust ranges anywhere from 3 to 10
                                    building a graph that’s right for your
                                    organization. If you reduce ranges, you lose
                                    the scores for the eliminated rows or
                                    columns.
                                </Trans>
                            </Text>

                            <Stack direction="row" gap="md" align="end">
                                <SelectField
                                    formId="risk-settings-form"
                                    label="Impact"
                                    name="impact-select"
                                    placeholder="Select impact level"
                                    options={impactOptions}
                                    value={impactOptions.find(
                                        (option) =>
                                            option.value ===
                                            String(riskSettings.impact),
                                    )}
                                    onChange={() => {
                                        // TODO: Implement impact change handler
                                    }}
                                />
                                <Icon name="Close" />
                                <SelectField
                                    formId="risk-settings-form"
                                    label="Likelihood"
                                    name="likelihood-select"
                                    placeholder="Select likelihood level"
                                    options={likelihoodOptions}
                                    value={likelihoodOptions.find(
                                        (option) =>
                                            option.value ===
                                            String(riskSettings.likelihood),
                                    )}
                                    onChange={() => {
                                        // TODO: Implement likelihood change handler
                                    }}
                                />
                                <Text size="500"> = </Text>
                                <RiskScore
                                    intensity="strong"
                                    severity={severity}
                                    scoreNumber={riskScore}
                                    size="md"
                                />
                            </Stack>

                            <Stack
                                direction="column"
                                gap="md"
                                data-testid="RiskLevelDefinitionsAccordion"
                            >
                                <Text size="300" type="title">
                                    <Trans>Risk Level Definitions</Trans>
                                </Text>
                                <Accordion
                                    title="Impact"
                                    data-id="impact-definitions"
                                    body={
                                        <Stack direction="column" gap="md">
                                            <TextField
                                                label={''}
                                                formId={''}
                                                name={undefined}
                                                value={undefined}
                                                onChange={undefined}
                                            />
                                        </Stack>
                                    }
                                />
                                <Accordion
                                    title="Likelihood"
                                    data-id="likelihood-definitions"
                                    body={
                                        <Stack direction="column" gap="md">
                                            <TextField
                                                label={''}
                                                formId={''}
                                                name={undefined}
                                                value={undefined}
                                                onChange={undefined}
                                            />
                                        </Stack>
                                    }
                                />
                            </Stack>

                            <Grid
                                gap="2xl"
                                data-testid="RiskRegisterSettingsThresholdView"
                                data-id="jW0kzbrb"
                            >
                                <Text
                                    size="300"
                                    type="title"
                                    id="threshold-settings-label"
                                >
                                    <Trans>Thresholds</Trans>
                                </Text>
                                <Text size="300" type="body">
                                    <Trans>
                                        Create your own custom scoring with 2 to
                                        5 thresholds and adjust the values as
                                        you wish. The default setting includes 4
                                        thresholds with distributed values.
                                    </Trans>
                                </Text>
                                <Threshold
                                    min={1}
                                    max={5}
                                    displaySplitCount={5}
                                    initialValues={[1, 2, 3, 4, 5]}
                                    aria-labelledby="threshold-settings-label"
                                    id="risk-threshold-settings"
                                    name="thresholdBoundaries"
                                    onValueChange={() => {
                                        // TODO: Implement threshold boundary updates
                                    }}
                                />
                                <Box>
                                    <Button
                                        label="Reset to defaults"
                                        level="tertiary"
                                        colorScheme="danger"
                                        onClick={() => {
                                            // TODO: Implement reset to defaults
                                        }}
                                    />
                                </Box>
                                <Text size="300" type="body">
                                    <Trans>
                                        Customize what each threshold is called
                                        and what it means. The name will show up
                                        when risk are selected and on the
                                        insight page’s legend. The description
                                        will show up on the legend.
                                    </Trans>
                                </Text>
                                {riskSettings.thresholds.map(
                                    (threshold, index) => {
                                        return (
                                            <ThresholdSettingsItem
                                                key={threshold.id}
                                                color={threshold.color}
                                                formId="risk-settings-scoring"
                                                index={index}
                                                data-id="4pUlWAt7"
                                                nameField={{
                                                    label: 'Name',
                                                    value: threshold.name,
                                                }}
                                                descriptionField={{
                                                    label: 'Description',
                                                    value: threshold.description,
                                                }}
                                            />
                                        );
                                    },
                                )}
                            </Grid>

                            <Box>
                                <Button
                                    type="submit"
                                    label="Save Settings"
                                    isLoading={isLoading}
                                    a11yLoadingLabel="Saving risk settings..."
                                />
                            </Box>
                        </Stack>
                    ),
                },
            }}
            onSubmit={handleSave}
        />
    );
});
