import { isEmpty } from 'lodash-es';
import { useRef } from 'react';
import {
    sharedRiskInsightsController,
    sharedRiskInsightsDownloadController,
    sharedRiskSettingsController,
} from '@controllers/risk';
import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import { Skeleton } from '@cosmos/components/skeleton';
import { DataHeatmap } from '@cosmos-lab/components/data-heatmap';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { riskHeatmapAdaptor } from './helper/risk-insights-heatmap.helper';

export const HeatmapCard = observer((): React.JSX.Element => {
    const { downloadRiskInsightsReportFromReference } =
        sharedRiskInsightsDownloadController;
    const captureAreaRef = useRef<HTMLDivElement>(null);

    const { riskInsights, isLoading } = sharedRiskInsightsController;
    const { riskSettings } = sharedRiskSettingsController;

    const { riskHeatmap = [] } = riskInsights ?? {};
    const { thresholds = [], impact = 0, likelihood = 0 } = riskSettings ?? {};

    if (isEmpty(thresholds) || !impact || !likelihood) {
        return <div></div>;
    }

    const onDownloadClick = () => {
        downloadRiskInsightsReportFromReference(captureAreaRef);
    };

    return (
        <Card
            title={t`Heatmap`}
            tooltipText={t`The risk heatmap shows how many risks you have based on impact and likelihood to help you prioritize.`}
            data-testid="HeatmapCard"
            data-id="BHJlG6TA"
            body={
                isLoading ? (
                    <Skeleton barCount={5} />
                ) : (
                    <Box ref={captureAreaRef}>
                        <DataHeatmap
                            yAxisSize={impact}
                            xAxisSize={likelihood}
                            data-id="heatmap-max-data-test-id"
                            values={riskHeatmapAdaptor(riskHeatmap, thresholds)}
                            thresholds={thresholds}
                        />
                    </Box>
                )
            }
            actions={[
                {
                    actionType: 'button',
                    id: 'add-auditor-action-button',
                    typeProps: {
                        label: '',
                        startIconName: 'Download',
                        level: 'tertiary',
                        onClick: onDownloadClick,
                    },
                },
            ]}
        />
    );
});
