import { useEffect, useMemo, useRef } from 'react';
import {
    sharedRiskInsightsController,
    sharedRiskInsightsDownloadController,
    sharedRiskSettingsController,
} from '@controllers/risk';
import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import { Skeleton } from '@cosmos/components/skeleton';
import { DataPosture } from '@cosmos-lab/components/data-posture';
import { t } from '@globals/i18n/macro';
import { observer, runInAction } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import { generateRiskPostureBoxes } from './helper/risk-posture.helper';

export const PostureCard = observer((): React.JSX.Element => {
    const captureRef = useRef<HTMLDivElement>(null);
    const { riskInsights, isLoading } = sharedRiskInsightsController;
    const { riskSettings } = sharedRiskSettingsController;
    const { currentWorkspace } = sharedWorkspacesController;
    const { downloadRiskInsightsReportFromReference } =
        sharedRiskInsightsDownloadController;
    const navigate = useNavigate();

    const { riskPosture = {} } = riskInsights ?? {};
    const { thresholds = [] } = riskSettings ?? {};

    const handleDownload = () => {
        downloadRiskInsightsReportFromReference(captureRef);
    };

    useEffect(() => {
        runInAction(() => {
            sharedRiskInsightsDownloadController.lastPostureRef = captureRef;
        });
    }, [captureRef]);

    const boxes = useMemo(() => {
        const generatedBoxes = generateRiskPostureBoxes(
            riskPosture,
            thresholds,
        );

        const sortedThresholds = [...thresholds].sort(
            (a, b) => a.minThreshold - b.minThreshold,
        );

        return sortedThresholds
            .map((threshold) => {
                const foundBox = generatedBoxes.find((box) =>
                    box.id.includes(`-${threshold.id}`),
                );

                return foundBox
                    ? {
                          ...foundBox,
                          legendLabel: threshold.name,
                          onClick: () => {
                              if (!currentWorkspace) {
                                  return;
                              }
                              navigate(
                                  `/workspaces/${currentWorkspace.id}/risk/register/management`,
                              );
                          },
                      }
                    : null;
            })
            .filter((box): box is NonNullable<typeof box> => box !== null);
    }, [riskPosture, thresholds, currentWorkspace, navigate]);

    return (
        <Card
            title={t`Posture`}
            tooltipText={t`Your risk posture shows your organization's overall preparedness against cyber attacks and other security threats.`}
            data-testid="PostureCard"
            data-id="MjCPGeLV"
            body={
                isLoading ? (
                    <Skeleton barCount={5} />
                ) : (
                    <Box ref={captureRef}>
                        <DataPosture legend size="lg" boxes={boxes} />
                    </Box>
                )
            }
            actions={[
                {
                    actionType: 'button',
                    id: 'download-posture-button',
                    typeProps: {
                        isIconOnly: true,
                        label: 'Download',
                        startIconName: 'Download',
                        level: 'tertiary',
                        onClick: handleDownload,
                    },
                },
            ]}
        />
    );
});
