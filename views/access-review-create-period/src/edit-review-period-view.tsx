import { useCallback } from 'react';
import {
    ApplicationsStep,
    PeriodsStep,
    ReviewersStep,
} from '@components/access-review';
import { sharedEditReviewPeriodController } from '@controllers/access-reviews';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { Wizard } from '@cosmos-lab/components/wizard';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import { useFormSubmit } from '@ui/forms';

interface PeriodsStepWizardProps {
    formRef: React.RefObject<HTMLFormElement>;
    onSubmit: () => void;
}

interface ApplicationsStepWizardProps {
    formRef: React.RefObject<HTMLFormElement>;
    onSubmit: () => void;
}

const PeriodsStepWizard = ({ formRef, onSubmit }: PeriodsStepWizardProps) => {
    return (
        <PeriodsStep
            formRef={formRef}
            formId="periods-step-form"
            data-testid="PeriodsStepWizard"
            data-id="rFQRzEra"
            onSubmit={onSubmit}
        />
    );
};

const ApplicationsStepWizard = ({
    formRef,
    onSubmit,
}: ApplicationsStepWizardProps) => {
    return (
        <ApplicationsStep
            formRef={formRef}
            formId="applications-step-form"
            data-testid="ApplicationsStepWizard"
            data-id="access-review-applications-step"
            onSubmit={onSubmit}
        />
    );
};

const ReviewersStepWizard = () => {
    return (
        <ReviewersStep data-testid="ReviewersStepWizard" data-id="yCP3J-Tq" />
    );
};

export const EditReviewPeriodView = observer((): React.JSX.Element => {
    const navigate = useNavigate();
    const { formRef: formDetailsRef, triggerSubmit: triggerSubmitDetails } =
        useFormSubmit();
    const {
        formRef: formApplicationsRef,
        triggerSubmit: triggerSubmitApplications,
    } = useFormSubmit();

    const { updateReviewPeriod, isReviewPeriodLoading } =
        sharedEditReviewPeriodController;

    const { currentWorkspaceId } = sharedWorkspacesController;

    const PeriodsStepComponent = useCallback(
        () => (
            <PeriodsStepWizard
                formRef={formDetailsRef}
                data-id="mx_Tx_MJ"
                onSubmit={triggerSubmitDetails}
            />
        ),
        [formDetailsRef, triggerSubmitDetails],
    );

    const ApplicationsStepComponent = useCallback(
        () => (
            <ApplicationsStepWizard
                formRef={formApplicationsRef}
                data-id="g9seORqj"
                onSubmit={triggerSubmitApplications}
            />
        ),
        [formApplicationsRef, triggerSubmitApplications],
    );

    const handleCancel = (): void => {
        if (!currentWorkspaceId) {
            return;
        }
        navigate(
            `/workspaces/${currentWorkspaceId}/governance/access-review/active`,
        );
    };

    const handleComplete = () => {
        updateReviewPeriod()
            .then(() => {
                navigate(
                    `/workspaces/${currentWorkspaceId}/governance/access-review/active`,
                );
            })
            .catch((error) => {
                console.error('Error updating review period:', error);
            });
    };

    if (isReviewPeriodLoading) {
        return (
            <Stack
                direction="column"
                align="center"
                justify="center"
                gap="6x"
                data-id="x9imC7OV"
                p="8x"
            >
                <Loader isSpinnerOnly label={t`Loading period details...`} />
            </Stack>
        );
    }

    return (
        <Stack
            direction="column"
            align="center"
            data-id="x9imC7OV"
            data-testid="EditReviewPeriodView"
        >
            <Wizard
                nextButtonLabel={t`Continue`}
                data-id="access-review-create-period-wizard"
                data-testid="AccessReviewCreatePeriodWizardView"
                completeButtonLabel={t`Complete set up`}
                steps={[
                    {
                        component: PeriodsStepComponent,
                        stepTitle: t`Period Details`,
                        stepSubtitle: t`Set the review period dates`,
                        isStepSkippable: false,
                        onStepChange: triggerSubmitDetails,
                    },
                    {
                        component: ApplicationsStepComponent,
                        stepTitle: t`Applications`,
                        stepSubtitle: t`Select applications to review`,
                        isStepSkippable: false,
                        onStepChange: triggerSubmitApplications,
                    },
                    {
                        component: ReviewersStepWizard,
                        stepTitle: t`Review`,
                        stepSubtitle: t`Assign reviewers and confirm`,
                        isStepSkippable: false,
                    },
                ]}
                onCancel={handleCancel}
                onComplete={handleComplete}
            />
        </Stack>
    );
});
