import { isEmpty } from 'lodash-es';
import { useEffect, useMemo } from 'react';
import {
    LinkedWorkspacesByEvidenceController,
    sharedControlLinkedWorkspacesController,
} from '@controllers/controls';
import type { Row } from '@cosmos/components/datatable';
import { Metadata } from '@cosmos/components/metadata';
import { Skeleton } from '@cosmos/components/skeleton';
import { TagGroup } from '@cosmos/components/tag-group';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import type { EvidenceUnionResponseDto } from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';

const MAX_VISIBLE_TAGS = 3;

export const LinkedWorkspacesCell = observer(
    ({ row }: { row: Row<EvidenceUnionResponseDto> }): React.ReactNode => {
        const { evidenceId } = row.original;
        const { controlLinkedWorkspacesGroup } =
            sharedControlLinkedWorkspacesController;

        const linkedWorkspacesByEvidenceController = useMemo(
            () => new LinkedWorkspacesByEvidenceController(),
            [],
        );

        const { load, evidenceLinkedWorkspaces, isLoading } =
            linkedWorkspacesByEvidenceController;

        useEffect(() => {
            if (!controlLinkedWorkspacesGroup?.id) {
                return;
            }
            load(evidenceId, controlLinkedWorkspacesGroup.id);
        }, [controlLinkedWorkspacesGroup?.id, load, evidenceId]);

        if (isLoading) {
            return <Skeleton />;
        }

        if (isEmpty(evidenceLinkedWorkspaces)) {
            return <EmptyValue label="—" />;
        }

        return (
            <TagGroup
                data-testid="LinkedWorkspacesCell"
                maxVisibleTags={MAX_VISIBLE_TAGS}
                data-id="wFybpdoc"
            >
                {evidenceLinkedWorkspaces.map(({ workspace: { id, name } }) => (
                    <Metadata
                        key={id}
                        colorScheme="neutral"
                        label={name}
                        type="tag"
                        data-id="t9Q-INQF"
                    />
                ))}
            </TagGroup>
        );
    },
);
