import { isEmpty } from 'lodash-es';
import { useEffect, useMemo } from 'react';
import {
    LinkedWorkspacesByPolicyController,
    sharedControlLinkedWorkspacesController,
} from '@controllers/controls';
import type { Row } from '@cosmos/components/datatable';
import { Metadata } from '@cosmos/components/metadata';
import { Skeleton } from '@cosmos/components/skeleton';
import { TagGroup } from '@cosmos/components/tag-group';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import type { PolicyWithControlWorkspaceGroupResponseDto } from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';

export const PolicyLinkedWorkspacesCell = observer(
    ({
        row,
    }: {
        row: Row<PolicyWithControlWorkspaceGroupResponseDto>;
    }): React.ReactNode => {
        const { policy } = row.original;
        const { id: policyId } = policy;
        const { controlLinkedWorkspacesGroup } =
            sharedControlLinkedWorkspacesController;

        const linkedWorkspacesByPolicyController = useMemo(
            () => new LinkedWorkspacesByPolicyController(),
            [],
        );

        const { load, policyLinkedWorkspaces, isLoading } =
            linkedWorkspacesByPolicyController;

        useEffect(() => {
            if (!controlLinkedWorkspacesGroup?.id) {
                return;
            }
            load(policyId, controlLinkedWorkspacesGroup.id);
        }, [controlLinkedWorkspacesGroup?.id, load, policyId]);

        if (isLoading) {
            return <Skeleton />;
        }

        if (isEmpty(policyLinkedWorkspaces)) {
            return <EmptyValue label="—" />;
        }

        return (
            <TagGroup
                data-testid="LinkedWorkspacesCell"
                data-id="ZkzQcMlO"
                maxVisibleTags={3}
            >
                {policyLinkedWorkspaces.map(({ workspace: { id, name } }) => (
                    <Metadata
                        key={id}
                        colorScheme="neutral"
                        label={name}
                        type="tag"
                        data-id="CNn7AY-S"
                    />
                ))}
            </TagGroup>
        );
    },
);
