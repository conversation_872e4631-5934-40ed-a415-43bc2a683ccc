import { isNil } from 'lodash-es';
import type React from 'react';
import { Metadata } from '@cosmos/components/metadata';
import { AvatarIdentity } from '@cosmos-lab/components/identity';
import type { PolicyResponseDto } from '@globals/api-sdk/types';
import { getUserInitials } from '@helpers/user';

export const PolicyOwnerCell = ({
    row,
}: {
    row: {
        original: {
            policy: PolicyResponseDto;
        };
    };
}): React.ReactNode => {
    const { currentOwner } = row.original.policy;

    if (isNil(currentOwner)) {
        return <Metadata colorScheme="neutral" label="-" type="tag" />;
    }

    const userName =
        `${currentOwner.firstName} ${currentOwner.lastName}`.trim();

    const initials = getUserInitials({
        firstName: currentOwner.firstName,
        lastName: currentOwner.lastName,
    });

    return (
        <AvatarIdentity
            primaryLabel={userName}
            tertiaryLabel={currentOwner.email}
            data-testid="PolicyOwnerCell"
            size="sm"
            imgSrc={currentOwner.avatarUrl ?? ''}
            fallbackText={initials}
            data-id="JTUUcCHp"
        />
    );
};
