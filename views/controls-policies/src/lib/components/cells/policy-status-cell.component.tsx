import { isNil } from 'lodash-es';
import { getPolicyStatusLabel } from '@components/policies';
import { Metadata } from '@cosmos/components/metadata';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import type { PolicyResponseDto } from '@globals/api-sdk/types';

export const PolicyStatusCell = ({
    row,
}: {
    row: {
        original: {
            policy: PolicyResponseDto;
        };
    };
}): React.ReactNode => {
    const { policy } = row.original;
    let latestVersionId = 0;
    let latestVersion = null;

    if (isNil(policy.versions)) {
        return <EmptyValue label="-" />;
    }

    for (const version of policy.versions) {
        if (version.id >= latestVersionId) {
            latestVersionId = version.id;
            latestVersion = version;
        }
    }

    if (isNil(latestVersion)) {
        return <EmptyValue label="-" />;
    }

    if (isNil(latestVersion.policyVersionStatus)) {
        return <Metadata colorScheme="neutral" label="New" type="status" />;
    }

    const { colorScheme, label } = getPolicyStatusLabel(
        latestVersion.policyVersionStatus,
    );

    return (
        <Metadata
            colorScheme={colorScheme}
            label={label}
            type="tag"
            data-testid="PolicyStatusCell"
            data-id="C9Fg36fS"
        />
    );
};
