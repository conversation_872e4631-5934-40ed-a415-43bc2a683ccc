import { openUnmapObjectFromControlModal } from '@components/controls';
import { sharedControlDetailsController } from '@controllers/controls';
import { Icon } from '@cosmos/components/icon';
import { SchemaDropdown } from '@cosmos/components/schema-dropdown';
import type { PolicyWithControlWorkspaceGroupResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';

interface PolicyActionsCellProps {
    row: {
        original: PolicyWithControlWorkspaceGroupResponseDto;
    };
}

export const PolicyActionsCell = ({
    row,
}: PolicyActionsCellProps): React.JSX.Element => {
    const handleUnmapPolicy = action(() => {
        const policyId = row.original.policy.id;
        const { controlId } = sharedControlDetailsController;

        if (!controlId) {
            return;
        }

        openUnmapObjectFromControlModal({
            controlId,
            objectId: policyId,
            objectType: 'POLICY',
        });
    });

    return (
        <SchemaDropdown
            isIconOnly
            label="Actions"
            level="tertiary"
            startIconName="Action"
            data-testid="PolicyActionsCell"
            data-id="s0nK5Fbh"
            colorScheme="neutral"
            items={[
                {
                    id: 'unmap-policy-option',
                    label: t`Unmap policy`,
                    type: 'item',
                    value: 'unmap',
                    startSlot: <Icon name="Unlink" size="200" />,
                    onClick: handleUnmapPolicy,
                },
            ]}
        />
    );
};
