import { isNil } from 'lodash-es';
import { Feedback } from '@cosmos/components/feedback';
import { Text } from '@cosmos/components/text';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import type { PolicyResponseDto } from '@globals/api-sdk/types';
import {
    compareDayIsPast,
    formatDate,
    isDateWithinNextMonths,
} from '@helpers/date-time';

export const PolicyRenewalDateCell = ({
    row,
}: {
    row: {
        original: {
            policy: PolicyResponseDto;
        };
    };
}): React.ReactNode => {
    const { policy } = row.original;
    const currentVersion = policy.versions?.[0];

    if (isNil(currentVersion?.renewalDate)) {
        return <EmptyValue label="-" />;
    }

    const hasExpiredRenewalDate = compareDayIsPast(currentVersion.renewalDate);
    const isWithinRenewalPeriod = isDateWithinNextMonths(
        2,
        currentVersion.renewalDate,
    );

    if (hasExpiredRenewalDate || isWithinRenewalPeriod) {
        const severity = hasExpiredRenewalDate ? 'critical' : 'warning';

        return (
            <Feedback
                data-testid="policy-renewal-date"
                severity={severity}
                title={formatDate('sentence', currentVersion.renewalDate)}
            />
        );
    }

    return (
        <Text data-testid="PolicyRenewalDateCell" data-id="z42CfFuW">
            {formatDate('sentence', currentVersion.renewalDate)}
        </Text>
    );
};
