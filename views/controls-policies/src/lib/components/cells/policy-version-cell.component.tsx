import { isNil } from 'lodash-es';
import type React from 'react';
import { Metadata } from '@cosmos/components/metadata';
import type { PolicyResponseDto } from '@globals/api-sdk/types';

export const PolicyVersionCell = ({
    row,
}: {
    row: {
        original: {
            policy: PolicyResponseDto;
        };
    };
}): React.ReactNode => {
    const { policy } = row.original;
    let latestVersionId = 0;
    let latestVersion = null;

    if (isNil(policy.versions)) {
        return null;
    }

    for (const version of policy.versions) {
        if (version.id >= latestVersionId) {
            latestVersionId = version.id;
            latestVersion = version;
        }
    }

    if (isNil(latestVersion)) {
        return <Metadata colorScheme="neutral" label="-" type="tag" />;
    }

    if (latestVersion.version > 0) {
        return (
            <Metadata
                label={`V${latestVersion.version}`}
                colorScheme="neutral"
                type="tag"
                data-testid="policy-version-test-id"
            />
        );
    }

    return (
        <Metadata
            label="Draft"
            colorScheme="neutral"
            type="tag"
            data-testid="PolicyVersionCell"
            data-id="TiP4CvDG"
        />
    );
};
