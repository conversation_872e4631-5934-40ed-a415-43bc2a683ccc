import {
    FlatfileEntityEnum,
    sharedFlatfileController,
} from '@controllers/flatfile';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { routeController } from '@controllers/route';
import type { DatatableProps } from '@cosmos/components/datatable';
import type { SchemaDropdownItemData } from '@cosmos/components/schema-dropdown';
import type { RiskWithCustomFieldsResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';

export class RiskRegisterManagementTableModel {
    constructor() {
        makeAutoObservable(this);
    }

    get tableActions(): DatatableProps<RiskWithCustomFieldsResponseDto>['tableActions'] {
        const items: SchemaDropdownItemData[] = [];
        const { hasRiskManagePermission, isReleaseBulkImportRiskEnabled } =
            sharedFeatureAccessModel;

        if (hasRiskManagePermission) {
            items.push({
                id: 'create-risk',
                'data-testid': 'CreateRiskAction',
                label: t`Create a single risk`,
                type: 'item',
                onClick: () => {
                    sharedProgrammaticNavigationController.navigateTo(
                        `${routeController.userPartOfUrl}/risk/register/create-risk`,
                    );
                },
            });
        }

        if (isReleaseBulkImportRiskEnabled) {
            items.push({
                id: 'import-risks',
                'data-testid': 'ImportRiskAction',
                label: t`Create/update risks in bulk`,
                type: 'item',
                onClick: () => {
                    sharedFlatfileController.createSpace({
                        entityType: FlatfileEntityEnum.RISK,
                    });
                },
            });
        }

        return [
            {
                actionType: 'dropdown',
                id: 'add-risk-dropdown',
                typeProps: {
                    label: t`Add risk`,
                    endIconName: 'ChevronDown',
                    level: 'primary',
                    items,
                },
            },
        ] as const;
    }
}

export const sharedRiskRegisterManagementTableModel =
    new RiskRegisterManagementTableModel();
