import { modalController } from '@controllers/modal';
import { action } from '@globals/mobx';
import { UPLOAD_RESPONSE_SECURITY_REVIEW_FILE_MODAL_ID } from '../constants/send-questionnaire-modal.constant';
import { UploadManualQuestionnaireModal } from '../modals/upload-manual-questionnaire-modal.component';

export const openUploadManualQuestionnaireModal = action((): void => {
    modalController.openModal({
        id: UPLOAD_RESPONSE_SECURITY_REVIEW_FILE_MODAL_ID,
        content: () => (
            <UploadManualQuestionnaireModal data-id="security-review-upload-manual-questionnaire" />
        ),
        centered: true,
        disableClickOutsideToClose: false,
        size: 'lg',
    });
});

export const closeUploadManualQuestionnaireModal = action((): void => {
    modalController.closeModal(UPLOAD_RESPONSE_SECURITY_REVIEW_FILE_MODAL_ID);
});
