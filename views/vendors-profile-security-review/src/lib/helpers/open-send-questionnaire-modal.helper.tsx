import { modalController } from '@controllers/modal';
import { action } from '@globals/mobx';
import { SEND_QUESTIONNAIRE_SECURITY_REVIEW_MODAL_ID } from '../constants/send-questionnaire-modal.constant';
import { SendQuestionnaireModal } from '../modals/send-questionnaire-modal.component';

export const openSendQuestionnaireModal = action((): void => {
    modalController.openModal({
        id: SEND_QUESTIONNAIRE_SECURITY_REVIEW_MODAL_ID,
        content: () => (
            <SendQuestionnaireModal data-id="security-review-send-questionnaire" />
        ),
        centered: true,
        disableClickOutsideToClose: false,
        size: 'lg',
    });
});

export const closeSendQuestionnaireModal = action((): void => {
    modalController.closeModal(SEND_QUESTIONNAIRE_SECURITY_REVIEW_MODAL_ID);
});
