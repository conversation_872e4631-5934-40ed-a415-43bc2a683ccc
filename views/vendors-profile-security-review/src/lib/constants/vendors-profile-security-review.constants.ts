import type { ComponentProps } from 'react';
import { openAddFilesModal } from '@components/vendors-security-reviews';
import type { Card } from '@cosmos/components/card';
import { t } from '@globals/i18n/macro';
import { openSendQuestionnaireModal } from '../helpers/open-send-questionnaire-modal.helper';
import { openUploadManualQuestionnaireModal } from '../helpers/open-upload-manual-questionnaire-modal.helper';

export const getVendorsProfileSecurityReviewAddQuestionnaireActions =
    (): ComponentProps<typeof Card>['actions'] => [
        {
            actionType: 'dropdown',
            id: 'vendors-profile-security-review-add-questionnaires-card',
            typeProps: {
                label: t`Add`,
                level: 'secondary',
                endIconName: 'ChevronDown',
                'data-id':
                    'vendors-profile-security-review-add-questionnaires-dropdown',
                items: [
                    {
                        id: 'vendors-profile-security-review-add-questionnaires-dropdown-first-item',
                        label: t`Send via Drata`,
                        type: 'item',
                        onClick: openSendQuestionnaireModal,
                    },
                    {
                        id: 'vendors-profile-security-review-add-questionnaires-dropdown-second-item',
                        label: t`Upload response file`,
                        type: 'item',
                        onClick: openUploadManualQuestionnaireModal,
                    },
                ],
            },
        },
    ];

export const getVendorsProfileSecurityReviewAddFilesActions =
    (): ComponentProps<typeof Card>['actions'] => [
        {
            actionType: 'button',
            id: 'vendors-profile-security-review-add-files-card',
            typeProps: {
                label: t`Add`,
                level: 'secondary',
                'data-id':
                    'vendors-profile-security-review-add-files-card-button',
                onClick: openAddFilesModal,
            },
        },
    ];
