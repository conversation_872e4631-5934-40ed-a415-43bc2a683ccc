import { isEmpty, isObject, uniqueId } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import {
    sharedVendorsSecurityReviewDetailsController,
    sharedVendorsSecurityReviewDocumentsController,
} from '@controllers/vendors';
import {
    vendorsQuestionnairesControllerSaveManualQuestionnaireFilesMutation,
    vendorsQuestionnairesControllerSendQuestionnaireEmailMutation,
    vendorsSecurityReviewsControllerCreateSecurityReviewDocumentMutation,
} from '@globals/api-sdk/queries';
import type { SendQuestionnaireRequestDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import type { FormValues } from '@ui/forms';
import { closeSendQuestionnaireModal } from '../helpers/open-send-questionnaire-modal.helper';
import { closeUploadManualQuestionnaireModal } from '../helpers/open-upload-manual-questionnaire-modal.helper';

class VendorsSecurityReviewQuestionnairesController {
    /**
     * State for tracking file processing.
     */
    currentFileIndex = 0;
    filesToProcess: File[] = [];
    successCount = 0;
    failureCount = 0;
    securityReviewId: number | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    vendorId: number | null = null;

    setVendorId = (vendorId: number): void => {
        this.vendorId = vendorId;
    };

    get currentVendorId(): number | null {
        return this.vendorId;
    }

    sendQuestionnaireEmailMutation = new ObservedMutation(
        vendorsQuestionnairesControllerSendQuestionnaireEmailMutation,
        {
            onError: () => {
                snackbarController.addSnackbar({
                    id: 'send-questionnaire-error',
                    props: {
                        title: t`Failed to send questionnaire`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        },
    );

    createSecurityReviewDocumentMutation = new ObservedMutation(
        vendorsSecurityReviewsControllerCreateSecurityReviewDocumentMutation,
        {
            onSuccess: () => {
                sharedVendorsSecurityReviewDocumentsController.securityReviewDocumentsQuery.invalidate();
            },
        },
    );

    saveManualQuestionnaireFilesMutation = new ObservedMutation(
        vendorsQuestionnairesControllerSaveManualQuestionnaireFilesMutation,
        {
            onSuccess: () => {
                sharedVendorsSecurityReviewDocumentsController.securityReviewDocumentsQuery.invalidate();
            },
        },
    );

    get isSendingQuestionnaire(): boolean {
        return (
            this.sendQuestionnaireEmailMutation.isPending ||
            this.createSecurityReviewDocumentMutation.isPending
        );
    }

    get isUploadingManualQuestionnaire(): boolean {
        return (
            this.saveManualQuestionnaireFilesMutation.isPending ||
            this.createSecurityReviewDocumentMutation.isPending
        );
    }

    get canManageQuestionnaires(): boolean {
        return (
            sharedFeatureAccessModel.isVendorEditable &&
            sharedFeatureAccessModel.canEditSecurityQuestionnaires
        );
    }

    saveSecurityReviewQuestionnaire = (formValues: FormValues): void => {
        if (!this.canManageQuestionnaires) {
            snackbarController.addSnackbar({
                id: 'send-questionnaire-permission-error',
                props: {
                    title: t`You don't have permission to send questionnaires`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        const { securityReviewDetails } =
            sharedVendorsSecurityReviewDetailsController;

        if (!securityReviewDetails?.id || !this.vendorId) {
            snackbarController.addSnackbar({
                id: 'send-questionnaire-missing-info-error',
                props: {
                    title: t`Security review or vendor information is missing`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        // Transform questionnaire data from form format to API format
        // The form returns questionnaires as {id, label, value} but API expects integer
        const questionnaireData = formValues.questionnaires as
            | { value: string }
            | string;
        const questionnaireId = isObject(questionnaireData)
            ? Number(questionnaireData.value)
            : Number(questionnaireData);

        if (!questionnaireId || isNaN(questionnaireId)) {
            snackbarController.addSnackbar({
                id: 'send-questionnaire-invalid-questionnaire-error',
                props: {
                    title: t`Please select a valid questionnaire`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        const requestData: SendQuestionnaireRequestDto = {
            email: String(formValues.sendTo)
                .replaceAll(', ', ';')
                .replaceAll(',', ';'),
            emailContent: String(formValues.message),
            questionnaireId,
            securityReviewId: securityReviewDetails.id,
        };

        // Step 1: Send the questionnaire email
        this.sendQuestionnaireEmailMutation.mutate({
            body: requestData,
            path: { id: this.vendorId },
        });

        // Step 2: Wait for the first mutation to complete, then create the security review document
        when(
            () => !this.sendQuestionnaireEmailMutation.isPending,
            () => {
                if (this.sendQuestionnaireEmailMutation.hasError) {
                    logger.error({
                        message: 'Failed to send questionnaire:',
                        additionalInfo: {
                            error: this.sendQuestionnaireEmailMutation.error,
                        },
                    });
                    snackbarController.addSnackbar({
                        id: 'send-questionnaire-error',
                        props: {
                            title: t`Failed to send questionnaire`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                // Get the questionnaire ID from the response
                const questionnaireResponse =
                    this.sendQuestionnaireEmailMutation.response;

                if (!questionnaireResponse?.id) {
                    snackbarController.addSnackbar({
                        id: 'send-questionnaire-error',
                        props: {
                            title: t`Failed to link questionnaire to security review`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                // Create the security review document link
                this.createSecurityReviewDocumentMutation.mutate({
                    body: {
                        documentId: questionnaireResponse.id,
                        type: 'QUESTIONNAIRE',
                    },
                    path: { id: securityReviewDetails.id },
                });

                // Wait for the second mutation to complete
                when(
                    () => !this.createSecurityReviewDocumentMutation.isPending,
                    () => {
                        if (
                            this.createSecurityReviewDocumentMutation.hasError
                        ) {
                            const error = this
                                .createSecurityReviewDocumentMutation
                                .error as Error & {
                                code?: number;
                            };
                            const errorCode = error.code;

                            // Check if this is the specific error code 28200 (document already exists)
                            if (errorCode === 28200) {
                                snackbarController.addSnackbar({
                                    id: 'send-questionnaire-resent-info',
                                    props: {
                                        title: t`Chosen questionnaire has been resent to vendor.`,
                                        severity: 'primary',
                                        closeButtonAriaLabel: t`Close`,
                                    },
                                });
                                closeSendQuestionnaireModal();

                                return;
                            }

                            logger.error({
                                message:
                                    'Failed to create security review document:',

                                additionalInfo: {
                                    error: this
                                        .createSecurityReviewDocumentMutation
                                        .error,
                                },
                            });
                            snackbarController.addSnackbar({
                                id: 'send-questionnaire-error',
                                props: {
                                    title: t`Failed to link questionnaire to security review`,
                                    severity: 'critical',
                                    closeButtonAriaLabel: t`Close`,
                                },
                            });

                            return;
                        }

                        // Show success message and close modal after both operations complete
                        snackbarController.addSnackbar({
                            id: 'send-questionnaire-success',
                            hasTimeout: true,
                            props: {
                                title: t`Questionnaire sent successfully`,
                                severity: 'success',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });
                        closeSendQuestionnaireModal();
                    },
                );
            },
        );
    };

    saveSecurityReviewManualQuestionnaire = (formValues: FormValues): void => {
        if (!this.canManageQuestionnaires) {
            snackbarController.addSnackbar({
                id: 'upload-questionnaire-permission-error',
                props: {
                    title: t`You don't have permission to upload questionnaires`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        const { securityReviewDetails } =
            sharedVendorsSecurityReviewDetailsController;

        if (!securityReviewDetails?.id || !this.vendorId) {
            snackbarController.addSnackbar({
                id: 'upload-questionnaire-missing-info-error',
                props: {
                    title: t`Security review or vendor information is missing`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        const files = formValues.files as File[] | undefined;

        if (!files || isEmpty(files)) {
            snackbarController.addSnackbar({
                id: 'upload-questionnaire-no-files-error',
                props: {
                    title: t`Please select at least one file to upload`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return;
        }

        this.resetFileProcessingState();
        this.filesToProcess = files;
        this.securityReviewId = securityReviewDetails.id;

        // Start processing the first file
        this.processNextFile();
    };

    resetFileProcessingState = (): void => {
        this.currentFileIndex = 0;
        this.successCount = 0;
        this.failureCount = 0;
    };

    processNextFile = (): void => {
        if (this.currentFileIndex >= this.filesToProcess.length) {
            // All files processed, show results
            this.showProcessingResults();

            return;
        }

        const currentFile = this.filesToProcess[this.currentFileIndex];

        // Step 1: Upload the individual file
        if (!this.vendorId) {
            logger.error('Vendor ID is missing during file upload');
            this.failureCount = this.failureCount + 1;
            this.currentFileIndex = this.currentFileIndex + 1;
            this.processNextFile();

            return;
        }

        this.saveManualQuestionnaireFilesMutation.mutate({
            body: {
                'uploadedFiles[]': [currentFile],
            },
            path: { id: this.vendorId },
        });

        // Wait for upload to complete
        when(
            () => !this.saveManualQuestionnaireFilesMutation.isPending,
            () => {
                if (this.saveManualQuestionnaireFilesMutation.hasError) {
                    logger.error({
                        message: `Failed to upload manual questionnaire file ${currentFile.name}:`,
                        additionalInfo: {
                            uploadError:
                                this.saveManualQuestionnaireFilesMutation.error,
                        },
                    });
                    this.failureCount = this.failureCount + 1;
                    this.currentFileIndex = this.currentFileIndex + 1;
                    this.processNextFile();

                    return;
                }

                // Get the questionnaire ID from the response
                const questionnaireResponse =
                    this.saveManualQuestionnaireFilesMutation.response;

                if (!questionnaireResponse?.id) {
                    logger.error(
                        `No questionnaire ID in response for file ${currentFile.name}`,
                    );
                    this.failureCount = this.failureCount + 1;
                    this.currentFileIndex = this.currentFileIndex + 1;
                    this.processNextFile();

                    return;
                }

                // Step 2: Create the security review document link
                if (!this.securityReviewId) {
                    logger.error(
                        'Security review ID is missing during document creation',
                    );
                    this.failureCount = this.failureCount + 1;
                    this.currentFileIndex = this.currentFileIndex + 1;
                    this.processNextFile();

                    return;
                }

                this.createSecurityReviewDocumentMutation.mutate({
                    body: {
                        documentId: questionnaireResponse.id,
                        type: 'QUESTIONNAIRE',
                    },
                    path: { id: this.securityReviewId },
                });

                // Wait for document creation to complete
                when(
                    () => !this.createSecurityReviewDocumentMutation.isPending,
                    () => {
                        if (
                            this.createSecurityReviewDocumentMutation.hasError
                        ) {
                            const error = this
                                .createSecurityReviewDocumentMutation
                                .error as Error & { code?: number };
                            const errorCode = error.code;

                            // Check if this is the specific error code 28200 (document already exists)
                            if (errorCode === 28200) {
                                this.successCount = this.successCount + 1;
                            } else {
                                logger.error({
                                    message: `Failed to create security review document for file ${currentFile.name}:`,
                                    additionalInfo: {
                                        documentError:
                                            this
                                                .createSecurityReviewDocumentMutation
                                                .error,
                                    },
                                });
                                this.failureCount = this.failureCount + 1;
                            }
                        } else {
                            this.successCount = this.successCount + 1;
                        }

                        // Move to next file
                        this.currentFileIndex = this.currentFileIndex + 1;
                        this.processNextFile();
                    },
                );
            },
        );
    };

    showProcessingResults = (): void => {
        const totalCount = this.filesToProcess.length;

        // Show appropriate snackbar messages based on results
        if (this.successCount === totalCount) {
            // Complete success scenario
            const { successCount } = this;
            const successMessage =
                successCount === 1
                    ? t`Manual questionnaire uploaded successfully`
                    : t`${successCount} manual questionnaires uploaded successfully`;

            snackbarController.addSnackbar({
                id: `upload-manual-questionnaire-success-${uniqueId()}`,
                hasTimeout: true,
                props: {
                    title: successMessage,
                    severity: 'success',
                    closeButtonAriaLabel: t`Close`,
                },
            });
            closeUploadManualQuestionnaireModal();
        } else if (this.successCount > 0) {
            // Partial success scenario
            const { successCount } = this;
            const { failureCount } = this;
            const successMessage =
                successCount === 1
                    ? t`Manual questionnaire uploaded successfully`
                    : t`${successCount} manual questionnaires uploaded successfully`;

            const errorMessage =
                failureCount === 1
                    ? t`Failed to upload 1 questionnaire`
                    : t`Failed to upload ${failureCount} questionnaires`;

            snackbarController.addSnackbar({
                id: `upload-manual-questionnaire-success-${uniqueId()}`,
                hasTimeout: true,
                props: {
                    title: successMessage,
                    severity: 'success',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            snackbarController.addSnackbar({
                id: `upload-manual-questionnaire-error-${uniqueId()}`,
                props: {
                    title: errorMessage,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });
            closeUploadManualQuestionnaireModal();
        } else {
            // Complete failure scenario
            snackbarController.addSnackbar({
                id: `upload-manual-questionnaire-error-${uniqueId()}`,
                props: {
                    title: t`Failed to upload questionnaires`,
                    description: t`All files failed to upload. Please try again later.`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });
        }
    };
}

export const sharedVendorsSecurityReviewQuestionnairesController =
    new VendorsSecurityReviewQuestionnairesController();
