import { uniqueId } from 'lodash-es';
import type { DocumentFiles } from '@controllers/vendors';
import type { RowDataProps } from '@cosmos-lab/components/simple-table';
import {
    StackedList,
    StackedListItem,
} from '@cosmos-lab/components/stacked-list';
import { VendorsSecurityReviewFile } from './vendors-security-review-file';

interface VendorsSecurityReviewFilesComponentProps {
    data: RowDataProps[];
    'data-testid'?: string;
    'data-id'?: string;
}

export const VendorsSecurityReviewFilesComponent = ({
    data,
    'data-testid': dataTestId,
    'data-id': dataId,
}: VendorsSecurityReviewFilesComponentProps): React.JSX.Element => {
    return (
        <StackedList data-id={dataId} data-testid={dataTestId}>
            {data.map((item) => {
                const fileName = item.name as string;
                const exceptions = item.value as string;
                const fileData = item as DocumentFiles;

                return (
                    <StackedListItem
                        key={uniqueId(fileName)}
                        data-id="TcJxpQbo"
                        primaryColumn={
                            <VendorsSecurityReviewFile
                                name={fileName}
                                exceptions={exceptions}
                                fileData={fileData}
                            />
                        }
                    />
                );
            })}
        </StackedList>
    );
};
