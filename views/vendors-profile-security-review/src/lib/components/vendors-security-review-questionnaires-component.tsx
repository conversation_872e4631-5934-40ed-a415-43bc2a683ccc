import { uniqueId } from 'lodash-es';
import type { QuestionnaireFiles } from '@controllers/vendors';
import {
    StackedList,
    StackedListItem,
} from '@cosmos-lab/components/stacked-list';
import { VendorsSecurityReviewQuestionnaire } from './vendors-security-review-questionnaire.tsx';

interface VendorsSecurityReviewQuestionnairesComponentProps {
    data: QuestionnaireFiles[];
    'data-testid'?: string;
    'data-id'?: string;
}

export const VendorsSecurityReviewQuestionnairesComponent = ({
    data,
    'data-testid': dataTestId,
    'data-id': dataId,
}: VendorsSecurityReviewQuestionnairesComponentProps): React.JSX.Element => {
    return (
        <StackedList data-id={dataId} data-testid={dataTestId}>
            {data.map((questionnaireData) => {
                return (
                    <StackedListItem
                        key={uniqueId(questionnaireData.name)}
                        data-id="V8SYIl5S"
                        primaryColumn={
                            <VendorsSecurityReviewQuestionnaire
                                questionnaireData={questionnaireData}
                            />
                        }
                    />
                );
            })}
        </StackedList>
    );
};
