import { routeController } from '@controllers/route';
import {
    type DocumentFiles,
    sharedVendorsDetailsController,
    sharedVendorsSecurityReviewFilesMutationController,
} from '@controllers/vendors';
import { Button } from '@cosmos/components/button';
import { Stack } from '@cosmos/components/stack';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useNavigate, useParams } from '@remix-run/react';

interface SecurityReviewFileActionsCellComponentProps {
    fileData?: DocumentFiles;
}

export const SecurityReviewFileActionsCellComponent = observer(
    ({
        fileData,
    }: SecurityReviewFileActionsCellComponentProps): React.JSX.Element => {
        const navigate = useNavigate();
        const { workspaceId, vendorId, securityReviewId } = useParams();

        const {
            isDownloadPending,
            isDeletePending,
            downloadFile,
            openDeleteConfirmationModal,
        } = sharedVendorsSecurityReviewFilesMutationController;

        const { isVendorEditable } = sharedFeatureAccessModel;
        const { isProspectiveVendor } = sharedVendorsDetailsController;

        const handleViewFile = () => {
            if (
                !fileData?.documentId ||
                !workspaceId ||
                !vendorId ||
                !securityReviewId
            ) {
                return;
            }

            const vendorType = isProspectiveVendor ? 'prospective' : 'current';
            const viewRoute = `${routeController.userPartOfUrl}/vendors/${vendorType}/${vendorId}/security-reviews/${securityReviewId}/files/${fileData.documentId}`;

            navigate(viewRoute);
        };

        const handleDownloadFile = () => {
            if (isDownloadPending || !fileData?.documentId) {
                return;
            }

            downloadFile(fileData.documentId);
        };

        const handleDeleteFile = () => {
            if (
                isDeletePending ||
                !fileData?.securityReviewDocumentId ||
                !fileData.name
            ) {
                return;
            }

            openDeleteConfirmationModal(
                fileData.securityReviewDocumentId,
                fileData.name,
            );
        };

        return (
            <Stack
                gap="1x"
                data-testid="SecurityReviewFileActionsCellComponent"
                data-id="7xbJM0NT"
                align="start"
            >
                <Button
                    label={t`View`}
                    level="tertiary"
                    onClick={handleViewFile}
                />

                <Button
                    isIconOnly
                    label={t`Download`}
                    startIconName="Download"
                    level="tertiary"
                    onClick={handleDownloadFile}
                />

                {isVendorEditable && (
                    <Button
                        isIconOnly
                        label={t`Delete`}
                        colorScheme="danger"
                        startIconName="Trash"
                        level="tertiary"
                        onClick={handleDeleteFile}
                    />
                )}
            </Stack>
        );
    },
);
