import type { QuestionnaireFiles } from '@controllers/vendors';
import { Stack } from '@cosmos/components/stack';
import { SecurityReviewDocumentNameCellComponent } from './security-review-document-name-cell-component';
import { SecurityReviewQuestionnaireActionsCellComponent } from './security-review-questionnaire-actions-cell-component';
import { SecurityReviewQuestionnaireStatusCellComponent } from './security-review-questionnaire-status-cell-component';

interface VendorsSecurityReviewQuestionnaireProps {
    questionnaireData: QuestionnaireFiles;
}

export const VendorsSecurityReviewQuestionnaire = ({
    questionnaireData,
}: VendorsSecurityReviewQuestionnaireProps): React.JSX.Element => {
    const { name, customData } = questionnaireData;

    return (
        <Stack
            gap="2x"
            data-testid="VendorsSecurityReviewQuestionnaire"
            data-id="vendors-security-review-questionnaire"
            align="center"
            justify="between"
        >
            <Stack flexBasis="30%" flexShrink="0">
                <SecurityReviewDocumentNameCellComponent cellData={name} />
            </Stack>
            <Stack flexBasis="40%" flexShrink="0">
                <SecurityReviewQuestionnaireStatusCellComponent
                    cellData={customData}
                />
            </Stack>
            <Stack flexBasis="30%" flexShrink="0" justify="end" pr="2x">
                <SecurityReviewQuestionnaireActionsCellComponent
                    questionnaireData={questionnaireData}
                />
            </Stack>
        </Stack>
    );
};
