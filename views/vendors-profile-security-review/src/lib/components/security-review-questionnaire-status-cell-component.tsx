import { QuestionnaireStatusCellComponent } from '@components/vendors-profile';
import type { QuestionnaireFiles } from '@controllers/vendors';

interface SecurityReviewQuestionnaireStatusCellComponentProps {
    cellData: QuestionnaireFiles['customData'];
}

export const SecurityReviewQuestionnaireStatusCellComponent = ({
    cellData,
}: SecurityReviewQuestionnaireStatusCellComponentProps): React.JSX.Element => {
    const {
        isCompleted = false,
        isManualUpload = false,
        dateSent = '',
        completedAt = '',
        reminderDate = '',
    } = cellData;

    return (
        <QuestionnaireStatusCellComponent
            isCompleted={isCompleted}
            isManualUpload={isManualUpload}
            dateSent={dateSent}
            completedAt={completedAt}
            reminderDate={reminderDate}
            data-testid="SecurityReviewQuestionnaireStatusCellComponent"
            data-id="34qP4uZO"
        />
    );
};
