import type { DocumentFiles } from '@controllers/vendors';
import { Box } from '@cosmos/components/box';
import { Stack } from '@cosmos/components/stack';
import { dimension24x, dimension120x } from '@cosmos/constants/tokens';
import { SecurityReviewDocumentNameCellComponent } from './security-review-document-name-cell-component';
import { SecurityReviewFileActionsCellComponent } from './security-review-file-actions-cell-component';
import { SecurityReviewFileExceptionCellComponent } from './security-review-file-exception-cell-component';

interface VendorsSecurityReviewFileProps {
    name: string;
    exceptions: string;
    fileData?: DocumentFiles;
}

export const VendorsSecurityReviewFile = ({
    name,
    exceptions,
    fileData,
}: VendorsSecurityReviewFileProps): React.JSX.Element => {
    return (
        <Stack
            direction="row"
            data-testid="VendorsSecurityReviewFile"
            data-id="vendors-security-review-file"
            align="center"
            justify="between"
        >
            <Box width={dimension120x}>
                <SecurityReviewDocumentNameCellComponent cellData={name} />
            </Box>

            <Box width={dimension24x}>
                <SecurityReviewFileExceptionCellComponent
                    cellData={exceptions}
                />
            </Box>

            <SecurityReviewFileActionsCellComponent fileData={fileData} />
        </Stack>
    );
};
