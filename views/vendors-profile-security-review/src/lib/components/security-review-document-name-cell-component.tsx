import { Text } from '@cosmos/components/text';

interface SecurityReviewDocumentNameCellComponentProps {
    cellData: string;
}

export const SecurityReviewDocumentNameCellComponent = ({
    cellData,
}: SecurityReviewDocumentNameCellComponentProps): React.JSX.Element => {
    return (
        <Text
            shouldWrap
            align="left"
            as="span"
            type="body"
            size="200"
            data-testid="SecurityReviewDocumentNameCellComponent"
            data-id="1DHspZ55"
        >
            {cellData}
        </Text>
    );
};
