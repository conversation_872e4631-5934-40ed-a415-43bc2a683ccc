import { uploadManualQuestionnaireSchema } from '@components/vendors-prospective-add';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { observer } from '@globals/mobx';
import { Form, useFormSubmit } from '@ui/forms';
import { sharedVendorsSecurityReviewQuestionnairesController } from '../controllers/vendors-security-review-questionnaires-controller';
import { closeUploadManualQuestionnaireModal } from '../helpers/open-upload-manual-questionnaire-modal.helper';

const FORM_ID = 'security-review-upload-manual-questionnaire-modal-form-id';

export const UploadManualQuestionnaireModal = observer(
    (): React.JSX.Element => {
        const {
            saveSecurityReviewManualQuestionnaire,
            isUploadingManualQuestionnaire,
        } = sharedVendorsSecurityReviewQuestionnairesController;
        const { formRef, triggerSubmit } = useFormSubmit();

        return (
            <>
                <Modal.Header
                    title={t`Manual Security Questionnaire Upload`}
                    closeButtonAriaLabel={t`Close manual questionnaire modal`}
                    onClose={closeUploadManualQuestionnaireModal}
                />
                <Modal.Body>
                    <Stack direction="column">
                        <Text>
                            {t`Use the section below to upload additional security
                            questionnaire documents. We will automatically
                            create a zip file containing all the files`}
                        </Text>
                        <Form
                            hasExternalSubmitButton
                            ref={formRef}
                            formId={FORM_ID}
                            data-id="security-review-upload-manual-questionnaire-modal-form-id"
                            schema={uploadManualQuestionnaireSchema()}
                            isReadOnly={isUploadingManualQuestionnaire}
                            onSubmit={(values) => {
                                saveSecurityReviewManualQuestionnaire(values);
                            }}
                        />
                    </Stack>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Cancel`,
                            level: 'secondary',
                            cosmosUseWithCaution_isDisabled:
                                isUploadingManualQuestionnaire,
                            onClick: closeUploadManualQuestionnaireModal,
                        },
                        {
                            label: t`Submit`,
                            level: 'primary',
                            colorScheme: 'primary',
                            isLoading: isUploadingManualQuestionnaire,
                            onClick: () => {
                                triggerSubmit().catch(() => {
                                    logger.error({
                                        message: 'Failed to submit form',
                                        additionalInfo: {
                                            isUploadingManualQuestionnaire,
                                        },
                                    });
                                });
                            },
                        },
                    ]}
                />
            </>
        );
    },
);
