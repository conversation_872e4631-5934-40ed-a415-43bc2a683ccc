import { capitalize, isEmpty, isNil } from 'lodash-es';
import { useMemo } from 'react';
import {
    sharedRiskCustomFieldsSubmissionsController,
    sharedRiskSettingsController,
} from '@controllers/risk';
import { Box } from '@cosmos/components/box';
import { Icon } from '@cosmos/components/icon';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Tooltip } from '@cosmos/components/tooltip';
import { AvatarStack } from '@cosmos-lab/components/avatar-stack';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import { RiskScore } from '@cosmos-lab/components/risk-score';
import type {
    RiskResponseDto,
    RiskUserResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { formatDate } from '@helpers/date-time';
import { getFullName, getInitials } from '@helpers/formatters';
import { getRiskTreatmentLabel } from '@helpers/risk-treatment';
import { sharedCustomFieldsManager } from '@models/custom-fields';
import { calculateRiskMetrics } from '../../helpers/risk-assessment.helper';

const MAX_REVIEWERS_TO_SHOW = 6;

interface TreatmentReadOnlyContentProps {
    riskDetails: RiskResponseDto | null;
}

export const TreatmentReadOnlyContent = observer(
    ({ riskDetails }: TreatmentReadOnlyContentProps) => {
        const { reviewers = [] } = riskDetails ?? {};
        const { riskSettings } = sharedRiskSettingsController;
        const {
            riskCustomFieldsTreatmentSection,
            isLoading: isCustomFieldsLoading,
        } = sharedRiskCustomFieldsSubmissionsController;

        const isUntreated = riskDetails?.treatmentPlan === 'UNTREATED';
        const isTransfer = riskDetails?.treatmentPlan === 'TRANSFER';
        const isMitigate = riskDetails?.treatmentPlan === 'MITIGATE';

        // Extract complex condition into a readable constant
        const shouldShowCustomFields =
            !isCustomFieldsLoading &&
            riskCustomFieldsTreatmentSection?.customFields &&
            !isEmpty(riskCustomFieldsTreatmentSection.customFields);

        // Calculate risk metrics for residual score
        const riskMetrics = useMemo(
            () =>
                calculateRiskMetrics(
                    riskDetails?.residualScore ?? 0,
                    riskSettings,
                ),
            [riskDetails?.residualScore, riskSettings],
        );

        return (
            <Stack direction="column" gap="xl" data-id="GNTELf4J">
                <KeyValuePair
                    label={t`Treatment option`}
                    value={
                        riskDetails?.treatmentPlan
                            ? getRiskTreatmentLabel(riskDetails.treatmentPlan)
                            : '—'
                    }
                />
                {!isUntreated && (
                    <>
                        <KeyValuePair
                            label={t`Treatment plan`}
                            value={
                                isEmpty(riskDetails?.treatmentDetails)
                                    ? '-'
                                    : riskDetails?.treatmentDetails
                            }
                        />

                        {/* Show residual fields for TRANSFER and MITIGATE */}
                        {(isTransfer || isMitigate) && (
                            <>
                                <Stack direction="row" gap="2xl">
                                    <KeyValuePair
                                        type="REACT_NODE"
                                        label={t`Residual impact`}
                                        value={
                                            isNil(
                                                riskDetails.residualImpact,
                                            ) ? (
                                                <EmptyValue label="Residual impact" />
                                            ) : (
                                                <Text>
                                                    {riskDetails.residualImpact.toString()}
                                                </Text>
                                            )
                                        }
                                    />
                                    <KeyValuePair
                                        type="REACT_NODE"
                                        label={t`Residual likelihood`}
                                        value={
                                            isNil(
                                                riskDetails.residualLikelihood,
                                            ) ? (
                                                <EmptyValue label="Residual likelihood" />
                                            ) : (
                                                <Text>
                                                    {riskDetails.residualLikelihood.toString()}
                                                </Text>
                                            )
                                        }
                                    />
                                </Stack>
                                <KeyValuePair
                                    type="REACT_NODE"
                                    label={t`Residual score`}
                                    value={
                                        <RiskScore
                                            intensity="moderate"
                                            size="md"
                                            severity={riskMetrics.severity}
                                            scoreNumber={
                                                riskDetails.residualScore ??
                                                null
                                            }
                                            label={capitalize(
                                                riskMetrics.threshold?.name,
                                            )}
                                        />
                                    }
                                />
                                <Box>
                                    <Tooltip
                                        text={t`This value represents the risk score after mitigating efforts have been completed, calculated by the Residual Impact times Residual Likelihood.`}
                                        data-id="assessment-score-tooltip"
                                    >
                                        <Stack
                                            direction="row"
                                            align="center"
                                            gap="xs"
                                        >
                                            <Text
                                                type="title"
                                                size="100"
                                                colorScheme="neutral"
                                            >
                                                {t`What does this score mean?`}
                                            </Text>
                                            <Icon
                                                name="Help"
                                                colorScheme="neutral"
                                            />
                                        </Stack>
                                    </Tooltip>
                                </Box>
                                <KeyValuePair
                                    label={t`Anticipated completion date`}
                                    value={
                                        riskDetails.anticipatedCompletionDate
                                            ? formatDate(
                                                  'sentence',
                                                  riskDetails.anticipatedCompletionDate,
                                              )
                                            : '—'
                                    }
                                />
                            </>
                        )}
                        <KeyValuePair
                            label={t`Completed date`}
                            value={
                                riskDetails?.completionDate
                                    ? formatDate(
                                          'sentence',
                                          riskDetails.completionDate,
                                      )
                                    : '—'
                            }
                        />
                        <KeyValuePair
                            type="REACT_NODE"
                            label={t`Reviewers`}
                            value={
                                isEmpty(reviewers) ? (
                                    <Text type="body" colorScheme="neutral">
                                        {t`No reviewers assigned`}
                                    </Text>
                                ) : (
                                    <AvatarStack
                                        data-id="Af2FFJcv"
                                        maxVisibleItems={MAX_REVIEWERS_TO_SHOW}
                                        data-testid="TreatmentReviewersContent"
                                        avatarData={reviewers.map(
                                            (reviewer: RiskUserResponseDto) => {
                                                const {
                                                    firstName,
                                                    lastName,
                                                    avatarUrl,
                                                    email,
                                                } = reviewer;
                                                const fullName = getFullName(
                                                    firstName,
                                                    lastName,
                                                );
                                                const fallbackText =
                                                    getInitials(fullName);

                                                return {
                                                    fallbackText,
                                                    primaryLabel: fullName,
                                                    secondaryLabel: email,
                                                    imgSrc: avatarUrl,
                                                };
                                            },
                                        )}
                                    />
                                )
                            }
                        />

                        {shouldShowCustomFields && (
                            <>
                                {sharedCustomFieldsManager.renderReadOnlyCustomFields(
                                    riskCustomFieldsTreatmentSection.customFields,
                                )}
                            </>
                        )}
                    </>
                )}

                {shouldShowCustomFields && (
                    <>
                        {sharedCustomFieldsManager.renderReadOnlyCustomFields(
                            riskCustomFieldsTreatmentSection.customFields,
                        )}
                    </>
                )}
            </Stack>
        );
    },
);
