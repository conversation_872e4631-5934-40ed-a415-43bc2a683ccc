import { useFormContext, useWatch } from 'react-hook-form';
import { Box } from '@cosmos/components/box';
import { Grid } from '@cosmos/components/grid';
import { Icon } from '@cosmos/components/icon';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Tooltip } from '@cosmos/components/tooltip';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { type FormSchema, UniversalRenderFields } from '@ui/forms';
import { ResidualScoreDisplay } from './residual-score-display.component';

interface ConditionalResidualScoreSectionProps {
    residualFields: FormSchema;
}

export const ConditionalResidualScoreSection = observer(
    ({
        residualFields,
    }: ConditionalResidualScoreSectionProps): React.JSX.Element | null => {
        const { control } = useFormContext();

        const treatmentPlan = useWatch({
            name: 'treatmentPlan',
            control,
        }) as { value: string } | null | undefined;

        const shouldShowResidualSection =
            treatmentPlan?.value === 'MITIGATE' ||
            treatmentPlan?.value === 'TRANSFER';

        if (!shouldShowResidualSection) {
            return null;
        }

        return (
            <Stack direction="column" gap="md" data-id="y7YV6kgp">
                <Text type="title" size="100">
                    {t`Residual score`}
                </Text>
                <Grid columns="2" gap="lg" align="end">
                    <UniversalRenderFields
                        fields={residualFields}
                        formId="treatment-edit-form"
                        data-id="treatment-edit-form-residual-fields"
                    />
                </Grid>
                <Stack direction="column">
                    <ResidualScoreDisplay data-id="-fWmwG3Y" />
                </Stack>
                <Box>
                    <Tooltip
                        text={t`This value represents the risk score after mitigating efforts have been completed, calculated by the Residual Impact times Residual Likelihood.`}
                        data-id="assessment-score-tooltip"
                    >
                        <Stack direction="row" align="center" gap="xs">
                            <Text type="title" size="100" colorScheme="neutral">
                                {t`What does this score mean?`}
                            </Text>
                            <Icon name="Help" colorScheme="neutral" />
                        </Stack>
                    </Tooltip>
                </Box>
            </Stack>
        );
    },
);
