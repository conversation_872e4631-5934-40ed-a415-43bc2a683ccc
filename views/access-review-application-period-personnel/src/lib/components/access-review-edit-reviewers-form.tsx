import { z } from 'zod';
import { sharedAccessReviewPeriodApplicationController } from '@controllers/access-reviews';
import { sharedUsersController } from '@controllers/users';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { sharedCurrentUserController } from '@globals/current-user';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getFullName, getInitials } from '@helpers/formatters';
import { Form, type FormValues } from '@ui/forms';

interface AccessReviewEditReviewersFormProps {
    formRef: React.ForwardedRef<HTMLFormElement>;
    onSubmit: (values: FormValues) => void;
}

export const AccessReviewEditReviewersForm = observer(
    ({
        formRef,
        onSubmit,
    }: AccessReviewEditReviewersFormProps): React.JSX.Element => {
        const { isLoading, loadReviewerUsers, fullUsers } =
            sharedUsersController;
        const { accessReviewPeriodApplicationReviewers } =
            sharedAccessReviewPeriodApplicationController;
        const { user: loggedUser } = sharedCurrentUserController;

        return (
            <Form
                hasExternalSubmitButton
                formId="access-review-edit-reviewers-form"
                ref={formRef}
                data-testid="AccessReviewEditReviewersForm"
                data-id="Ndqu8Tbm"
                schema={{
                    reviewers: {
                        type: 'combobox',
                        label: t`Reviewers`,
                        initialValue:
                            accessReviewPeriodApplicationReviewers.map(
                                (reviewer) => ({
                                    id: String(reviewer.id),
                                    label: getFullName(
                                        reviewer.firstName,
                                        reviewer.lastName,
                                    ),
                                    value: String(reviewer.id),
                                    avatar: {
                                        fallbackText: getInitials(
                                            getFullName(
                                                reviewer.firstName,
                                                reviewer.lastName,
                                            ),
                                        ),
                                        imgAlt: getFullName(
                                            reviewer.firstName,
                                            reviewer.lastName,
                                        ),
                                    },
                                }),
                            ) as unknown as ListBoxItemData[],
                        isMultiSelect: true,
                        isLoading,
                        removeAllSelectedItemsLabel: t`Clear all`,
                        getSearchEmptyState: () => t`No employees found`,
                        loaderLabel: t`Loading`,
                        placeholder: t`Search by name or email`,
                        options: fullUsers,
                        onFetchOptions: loadReviewerUsers,
                        validator: z
                            .array(
                                z.object({
                                    value: z.string(),
                                }),
                            )
                            .min(1, {
                                message: t`Application must have at least 1 reviewer`,
                            })
                            .refine(
                                (items) =>
                                    !(
                                        items.length === 1 &&
                                        items[0].value ===
                                            String(loggedUser?.id)
                                    ),
                                {
                                    message: t`Reviewers cannot review their own status. Please add another reviewer.`,
                                },
                            ),
                    },
                }}
                onSubmit={onSubmit}
            />
        );
    },
);
