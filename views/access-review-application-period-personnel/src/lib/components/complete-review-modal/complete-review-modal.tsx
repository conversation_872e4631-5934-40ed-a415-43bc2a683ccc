import { useMemo, useState } from 'react';
import type { SubmitHandler } from 'react-hook-form';
import {
    AdditionalEvidenceForm<PERSON>ield,
    ReviewStatusFormField,
} from '@components/access-review-details';
import {
    sharedAccessReviewPeriodApplicationController,
    sharedAccessReviewPeriodApplicationSummaryController,
} from '@controllers/access-reviews';
import { activeAccessReviewsApplicationsController } from '@controllers/access-reviews-applications';
import { modalController } from '@controllers/modal';
import { Banner } from '@cosmos/components/banner';
import type { SUPPORTED_FORMATS } from '@cosmos/components/file-upload';
import { Loader } from '@cosmos/components/loader';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type { AccessReviewApplicationControllerUpdateAccessApplicationStatusData } from '@globals/api-sdk/types';
import { t, Trans } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import {
    Form,
    type FormSchema,
    type FormValues,
    useFormSubmit,
} from '@ui/forms';

const ACCEPTED_FILE_FORMATS = [
    'pdf',
    'docx',
    'odt',
    'xlsx',
    'ods',
    'pptx',
    'odp',
    'gif',
    'jpeg',
    'png',
] as const satisfies (typeof SUPPORTED_FORMATS)[keyof typeof SUPPORTED_FORMATS][];

const MODAL_ID = 'access-review-complete-application-modal';
const FORM_ID = 'complete-review-form';

const MINIMUM_VALUE = 1;

const handleClose = (): void => {
    modalController.closeModal(MODAL_ID);
};

export const CompleteReviewModal = observer((): React.JSX.Element => {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const {
        totalRejected,
        totalNotReviewed,
        isLoading,
        applicationId,
        periodId,
    } = sharedAccessReviewPeriodApplicationSummaryController;
    const { isManuallyAddedApplication } =
        sharedAccessReviewPeriodApplicationController;
    const { completeReviewApplication } =
        activeAccessReviewsApplicationsController;

    const { formRef, triggerSubmit } = useFormSubmit();

    const handleSubmit = action<SubmitHandler<FormValues>>(
        (values: FormValues) => {
            setIsSubmitting(true);
            const typedValues = values as {
                reviewStatus: AccessReviewApplicationControllerUpdateAccessApplicationStatusData['body']['reviewStatus'];
                additionalEvidence: File[];
            };

            completeReviewApplication(
                periodId,
                applicationId,
                'ONE_YEAR',
                typedValues.reviewStatus ?? 'OUT_OF_SCOPE',
                typedValues.additionalEvidence,
            );

            handleClose();
        },
    );

    if (isLoading) {
        return (
            <Modal.Body>
                <Loader isSpinnerOnly label={t`Loading...`} />
            </Modal.Body>
        );
    }

    const schema = useMemo((): FormSchema => {
        if (totalNotReviewed >= MINIMUM_VALUE) {
            return {
                reviewStatus: {
                    type: 'custom',
                    label: t`Review status`,
                    initialValue: 'OUT_OF_SCOPE',
                    render: ReviewStatusFormField,
                    validateWithDefault: 'radioGroup',
                    options: [
                        { label: t`Approved`, value: 'APPROVED' },
                        { label: t`Rejected`, value: 'REJECTED' },
                        { label: t`Out of scope`, value: 'OUT_OF_SCOPE' },
                    ],
                    cosmosUseWithCaution_forceOptionOrientation: 'vertical',
                },
                additionalEvidence: {
                    type: 'custom',
                    label: t`Additional evidence`,
                    render: AdditionalEvidenceFormField,
                    isOptional: true,
                    validateWithDefault: 'file',
                    acceptedFormats: ACCEPTED_FILE_FORMATS,
                },
            };
        }

        return {
            additionalEvidence: {
                type: 'custom',
                label: isManuallyAddedApplication
                    ? t`Evidence`
                    : t`Additional evidence`,
                render: AdditionalEvidenceFormField,
                isOptional: !isManuallyAddedApplication,
                validateWithDefault: 'file',
                acceptedFormats: ACCEPTED_FILE_FORMATS,
            },
        };
    }, [totalNotReviewed, isManuallyAddedApplication]);

    return (
        <>
            <Modal.Header
                title={t`Complete review`}
                closeButtonAriaLabel={t`Close complete review modal`}
                onClose={handleClose}
            />
            <Modal.Body
                data-id="complete-review-modal-body"
                data-testid="CompleteReviewModal"
            >
                <Stack gap="8x" direction="column">
                    {(totalRejected >= MINIMUM_VALUE ||
                        totalNotReviewed >= MINIMUM_VALUE) && (
                        <Stack gap="4x" direction="column">
                            {totalRejected >= MINIMUM_VALUE && (
                                <Banner
                                    title={t`This application has ${totalRejected} personnel with access level as “Rejected”`}
                                    severity="warning"
                                    body={
                                        <Text as="p" colorScheme="inherit">
                                            <Trans>
                                                Completing this review will keep
                                                the “Rejected” personnel access
                                                as “Rejected” and will be
                                                visible in the evidence that
                                                Drata generates.
                                            </Trans>
                                        </Text>
                                    }
                                />
                            )}
                        </Stack>
                    )}
                    <Form
                        hasExternalSubmitButton
                        ref={formRef}
                        data-id="complete-review-form"
                        formId={FORM_ID}
                        schema={schema}
                        onSubmit={(values: FormValues) => handleSubmit(values)}
                    />
                </Stack>
            </Modal.Body>
            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Cancel`,
                        level: 'secondary',
                        onClick: handleClose,
                    },
                    {
                        label: t`Complete`,
                        isLoading: isSubmitting,
                        onClick: () => {
                            triggerSubmit().catch(() => {
                                // not handling errors here
                                console.error('Failed to submit form');
                            });
                        },
                    },
                ]}
            />
        </>
    );
});
