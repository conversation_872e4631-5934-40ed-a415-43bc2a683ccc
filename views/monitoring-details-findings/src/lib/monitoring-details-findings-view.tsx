import { isEmpty } from 'lodash-es';
import { useCallback, useRef } from 'react';
import { AppDatatable } from '@components/app-datatable';
import {
    sharedFindingsController,
    sharedFindingsFiltersController,
} from '@controllers/monitoring-details';
import { panelController } from '@controllers/panel';
import type {
    DatatableProps,
    DatatableRef,
    FetchDataResponseParams,
    FilterProps,
} from '@cosmos/components/datatable';
import type { FindingItemResponseDto } from '@globals/api-sdk/types';
import { observer, runInAction } from '@globals/mobx';
import {
    MONITORING_DETAILS_FINDINGS_PANEL_ID,
    MonitoringDetailsFindingsPanelView,
    sharedMonitoringDetailsFindingsPanelModel,
} from '@views/monitoring-details-findings-panel';
import { sharedMonitoringDetailsFindingsModel } from './models/monitoring-details-findings.model';

// Both the PRODUCTION and CODE views of monitoring use this single table, yet each view has their own unique controller.
// TODO: Clean up the view/controller pattern to better support this implementation.
export const MonitoringDetailsFindingsView = observer((): React.JSX.Element => {
    const datatableRef = useRef<DatatableRef>(null);

    sharedMonitoringDetailsFindingsModel.datatableRef = datatableRef;

    const {
        findingsList,
        findingsListTotal,
        isLoadingFindingsList,
        emptyState,
    } = sharedFindingsController;

    const { findingsFilters, isLoadingFindingsFilters } =
        sharedFindingsFiltersController;
    const currentTestId = findingsFilters?.testId;

    const preventSelectAllAcrossPages = useCallback(() => true, []);

    const openFindingPanel = useCallback(
        ({ row }: { row: FindingItemResponseDto }) => {
            runInAction(() => {
                sharedMonitoringDetailsFindingsPanelModel.selectedFindingItem =
                    row;
                panelController.openPanel({
                    id: MONITORING_DETAILS_FINDINGS_PANEL_ID,
                    queryParams: {
                        panelEntityId: row.id,
                        panelEntityFindingId: row.findingId,
                        panelConnectionId: row.connectionId,
                    },
                    content: () => (
                        <MonitoringDetailsFindingsPanelView data-id="finding-details-panel" />
                    ),
                });
            });
        },
        [],
    );

    const loadFindings = useCallback(
        (params: FetchDataResponseParams) => {
            if (currentTestId) {
                sharedFindingsController.load({
                    testId: Number(currentTestId),
                    ...params,
                });
            }
        },
        [currentTestId],
    );

    const {
        handleRowSelection,
        getMonitoringDetailsFindingsBulkActions,
        datatableStructure: { columns: structureColumns, filters },
    } = sharedMonitoringDetailsFindingsModel;

    return (
        <AppDatatable
            imperativeHandleRef={datatableRef}
            getRowId={(row) => row.findingId}
            isRowSelectionEnabled={preventSelectAllAcrossPages}
            tableId="datatable-findings"
            data-id="datatable-findings"
            data={findingsList}
            total={findingsListTotal}
            data-testid="MonitoringDetailsFindingsView"
            isLoading={isLoadingFindingsList || isLoadingFindingsFilters}
            bulkActionDropdownItems={getMonitoringDetailsFindingsBulkActions}
            emptyStateProps={emptyState}
            columns={
                structureColumns as DatatableProps<FindingItemResponseDto>['columns']
            }
            filterProps={
                isEmpty(filters)
                    ? undefined
                    : {
                          filters: filters as FilterProps['filters'],
                          clearAllButtonLabel: 'Reset filters',
                          triggerLabel: 'Filter',
                      }
            }
            filterViewModeProps={{
                props: {
                    selectedOption: 'pinned',
                    initialSelectedOption: 'pinned',
                    togglePinnedLabel: 'Pin filters to page',
                    toggleUnpinnedLabel: 'Move filters to dropdown',
                },
                viewMode: 'toggleable',
            }}
            onRowClick={openFindingPanel}
            onFetchData={loadFindings}
            onRowSelection={handleRowSelection}
        />
    );
});
