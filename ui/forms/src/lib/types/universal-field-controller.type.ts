import type { use<PERSON><PERSON>roller } from 'react-hook-form';
import type { FieldMetaByType } from './field-meta-by-type.type';
import type { FieldSchema } from './field-schema.type';
import type { FieldSchemaWithGroup } from './field-schema-with-group.type';
import type { FieldType } from './field-type.type';
import type { SharedProps } from './shared-props.type';

export type UniversalFieldController<T extends FieldType = FieldType> = {
    type: FieldSchemaWithGroup['type'];
    isOptional?: boolean;
    isShown: boolean;
    fieldSchemaProps: FieldSchema;
    setValue: (value: FieldMetaByType[T]['initialValue']) => void;
    required: SharedProps['required'];
    feedback: SharedProps['feedback'];
    optionalText: SharedProps['optionalText'];
} & ReturnType<typeof useController>['field'] &
    ReturnType<typeof useController>['fieldState'];
