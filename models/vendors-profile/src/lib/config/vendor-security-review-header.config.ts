import { isNil } from 'lodash-es';
import type { UserValue } from '@cosmos/components/key-value-pair';
import type {
    UserResponseDto,
    VendorResponseDto,
    VendorSecurityReviewResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { getFullName, getInitials } from '@helpers/formatters';

export const buildStatus = (
    status: VendorSecurityReviewResponseDto['status'],
): string => {
    switch (status) {
        case 'COMPLETED': {
            return t`Completed`;
        }
        case 'IN_PROGRESS': {
            return t`In progress`;
        }
        case 'NOT_YET_STARTED': {
            return t`Not yet started`;
        }
        default: {
            return '-';
        }
    }
};

export const buildImpactLevel = (
    impactLevel: VendorResponseDto['impactLevel'] | undefined,
): string => {
    switch (impactLevel) {
        case 'INSIGNIFICANT': {
            return t`Insignificant`;
        }
        case 'MINOR': {
            return t`Minor`;
        }
        case 'MODERATE': {
            return t`Moderate`;
        }
        case 'MAJOR': {
            return t`Major`;
        }
        case 'CRITICAL': {
            return t`Critical`;
        }
        case 'UNSCORED': {
            return t`Unscored`;
        }
        default: {
            return '-';
        }
    }
};

export const buildVendorStatus = (
    status: VendorResponseDto['status'] | undefined,
): string => {
    switch (status) {
        case 'PROSPECTIVE': {
            return t`Prospective`;
        }
        case 'ACTIVE': {
            return t`Active`;
        }
        case 'ARCHIVED': {
            return t`Archived`;
        }
        case 'APPROVED': {
            return t`Approved`;
        }
        case 'REJECTED': {
            return t`Rejected`;
        }
        case 'FLAGGED': {
            return t`Flagged`;
        }
        case 'ON_HOLD': {
            return t`On hold`;
        }
        case 'OFFBOARDED': {
            return t`Offboarded`;
        }
        case 'UNDER_REVIEW': {
            return t`Under review`;
        }
        case 'NONE': {
            return t`None`;
        }
        default: {
            return '-';
        }
    }
};

export const buildRequesterUser = (
    user: UserResponseDto | undefined | null,
): UserValue => {
    if (isNil(user)) {
        return {
            username: '',
            avatarProps: {
                fallbackText: '',
                imgSrc: '',
                imgAlt: '',
            },
        };
    }

    const fullName = getFullName(user.firstName, user.lastName);

    return {
        username: fullName,
        avatarProps: {
            fallbackText: getInitials(fullName),
            imgSrc: user.avatarUrl ?? '',
            imgAlt: getInitials(fullName),
        },
    };
};
