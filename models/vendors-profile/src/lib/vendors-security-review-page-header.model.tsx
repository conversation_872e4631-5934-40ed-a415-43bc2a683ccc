import { sharedVendorsSecurityReviewDetailsController } from '@controllers/vendors';
import { ActionStack } from '@cosmos/components/action-stack';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import { dimension3x } from '@cosmos/constants/tokens';
import type { Breadcrumb } from '@cosmos-lab/components/breadcrumbs';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { formatDate } from '@helpers/date-time';
import { getFullName } from '@helpers/formatters';
import {
    buildImpactLevel,
    buildStatus,
} from './config/vendor-security-review-header.config';
import { getHeaderStackActions } from './helpers/vendor-security-review-header.helper';
import type { VendorCategory } from './types/vendor-types';

export class VendorsSecurityReviewPageHeaderModel {
    #vendorType: VendorCategory = 'current';

    constructor() {
        makeAutoObservable(this);
    }

    pageId = 'vendors-profile-security-review-page';

    get breadcrumbs(): Breadcrumb[] {
        const { securityReviewDetails } =
            sharedVendorsSecurityReviewDetailsController;
        const { vendor } = securityReviewDetails ?? {};
        const vendorId = vendor?.id;
        const vendorTypePrefix = this.#vendorType;

        return [
            { label: t`Vendors`, pathname: `vendors/${vendorTypePrefix}` },
            {
                label: vendor?.name ?? t`Vendor`,
                pathname: `vendors/${vendorTypePrefix}/${vendorId}/overview`,
            },
            {
                label: t`Security reviews`,
                pathname: `vendors/${vendorTypePrefix}/${vendorId}/security-reviews`,
            },
        ];
    }

    get actionStack(): React.JSX.Element {
        const { securityReviewDetails } =
            sharedVendorsSecurityReviewDetailsController;

        const securityReviewId = securityReviewDetails?.id;
        const vendorId = securityReviewDetails?.vendor?.id;

        return (
            <ActionStack
                data-id="vendors-profile-security-review-page-action-stack"
                gap={dimension3x}
                stacks={getHeaderStackActions(securityReviewId, vendorId)}
            />
        );
    }

    get title(): string {
        const { securityReviewDetails } =
            sharedVendorsSecurityReviewDetailsController;

        if (!securityReviewDetails) {
            return '';
        }

        return securityReviewDetails.title ?? t`Untitled security review`;
    }

    get keyValuePairs(): KeyValuePairProps[] {
        const { securityReviewDetails } =
            sharedVendorsSecurityReviewDetailsController;

        if (!securityReviewDetails) {
            return [];
        }

        const { vendor, status, reviewDeadlineAt } = securityReviewDetails;

        const isCompleted = status === 'COMPLETED';

        const kvpStatus: KeyValuePairProps = {
            id: 'vendor-security-review-header-status',
            'data-id': 'vendor-security-review-header-status-id',
            label: t`Status`,
            value: {
                label: buildStatus(status),
                type: 'status',
                colorScheme: isCompleted ? 'success' : 'neutral',
            },
            type: 'BADGE',
        };

        if (vendor?.status !== 'PROSPECTIVE') {
            return [
                kvpStatus,
                {
                    id: 'vendor-security-review-header-impact-level',
                    'data-id': 'vendor-security-review-header-impact-level-id',
                    label: t`Impact level`,
                    value: buildImpactLevel(vendor?.impactLevel),
                    type: 'TEXT',
                },
            ];
        }

        return [
            kvpStatus,
            {
                id: 'vendor-security-review-header-requester',
                'data-id': 'vendor-security-review-header-requester',
                label: t`Requester`,
                value: getFullName(
                    vendor.user?.firstName,
                    vendor.user?.lastName,
                ),
                type: 'TEXT',
            },
            {
                id: 'vendor-security-review-header-review-deadline',
                'data-id': 'vendor-security-review-header-review-deadline',
                label: t`Review deadline`,
                value: formatDate('sentence', reviewDeadlineAt),
                type: 'TEXT',
            },
        ];
    }

    setVendorType(type: VendorCategory): void {
        this.#vendorType = type;
    }
}
