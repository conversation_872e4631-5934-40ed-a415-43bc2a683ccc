import type { ComponentProps } from 'react';
import { openFinalizeReviewModal } from '@components/vendors-security-reviews';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { routeController } from '@controllers/route';
import {
    sharedVendorsDetailsController,
    sharedVendorsSecurityReviewMutationController,
} from '@controllers/vendors';
import type { ActionStack } from '@cosmos/components/action-stack';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';

export const handleDeleteReview = action(
    (securityReviewId: number, vendorId: number): void => {
        openConfirmationModal({
            title: t`Delete Security Review`,
            body: t`Confirm that you'd like to delete this Security Review. Any uploaded documents to this review will also be removed.`,
            confirmText: t`Yes, delete review`,
            cancelText: t`No, take me back`,
            type: 'danger',
            size: 'md',
            onConfirm: action(() => {
                const { isProspectiveVendor } = sharedVendorsDetailsController;
                const vendorType = isProspectiveVendor
                    ? 'prospective'
                    : 'current';

                const redirectPath = `${routeController.userPartOfUrl}/vendors/${vendorType}/${vendorId}/overview`;

                sharedVendorsSecurityReviewMutationController.deleteSecurityReviewWithCallback(
                    securityReviewId,
                    () => {
                        sharedProgrammaticNavigationController.navigateTo(
                            redirectPath,
                        );
                    },
                );
            }),
            onCancel: () => {
                closeConfirmationModal();
            },
        });
    },
);

export const handleFinalizeReview = action((): void => {
    const { isVendorEditable } = sharedFeatureAccessModel;

    if (!isVendorEditable) {
        return;
    }

    openFinalizeReviewModal();
});

export const getHeaderStackActions = (
    securityReviewId?: number,
    vendorId?: number,
): ComponentProps<typeof ActionStack>['stacks'] => {
    const { isVendorEditable } = sharedFeatureAccessModel;

    const dropdownItems = [];

    if (isVendorEditable && securityReviewId && vendorId) {
        dropdownItems.push({
            id: 'vendor-security-review-header-delete-review',
            label: t`Delete review`,
            type: 'item' as const,
            value: 'DELETE_VENDOR',
            colorScheme: 'critical' as const,
            onSelect: () => {
                handleDeleteReview(securityReviewId, vendorId);
            },
        });
    }

    return [
        {
            actions: [
                {
                    actionType: 'dropdown',
                    id: 'vendor-security-review-header-dropdown',
                    typeProps: {
                        isIconOnly: true,
                        startIconName: 'HorizontalMenu',
                        level: 'tertiary',
                        label: t`Options`,
                        align: 'end',
                        items: dropdownItems,
                    },
                },
                {
                    actionType: 'button',
                    id: 'vendor-security-review-header-finalize-review',
                    typeProps: {
                        label: t`Finalize review`,
                        colorScheme: 'primary',
                        onClick: handleFinalizeReview,
                    },
                },
            ],
            id: 'vendor-security-review-header-actions-stack',
        },
    ] as const;
};
