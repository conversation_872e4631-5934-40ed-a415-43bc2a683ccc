import { capitalize, head, isEmpty, isNil } from 'lodash-es';
import { getCategoryLabel } from '@components/monitoring';
import {
    activeMonitoringController,
    sharedMonitoringDetailsActionsController,
} from '@controllers/monitoring-details';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { type Action, ActionStack } from '@cosmos/components/action-stack';
import { Banner } from '@cosmos/components/banner';
import { Button, type ButtonLevel } from '@cosmos/components/button';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import {
    type ColorScheme,
    Metadata,
    type MetadataProps,
} from '@cosmos/components/metadata';
import { dimensionSm } from '@cosmos/constants/tokens';
import type { MonitorV2ControlTestInstanceOverviewResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { formatDate } from '@helpers/date-time';
import { CheckResultStatus, CheckStatus } from '@helpers/evidence';
import { getTestLifecycleLabel } from '@views/monitoring';

const getStatusColorScheme = (
    status: MonitorV2ControlTestInstanceOverviewResponseDto['checkResultStatus'],
): ColorScheme => {
    if (status === 'PASSED') {
        return 'success';
    }

    if (status === 'FAILED') {
        return 'critical';
    }

    if (status === 'ERROR') {
        return 'warning';
    }

    return 'neutral';
};

const getMonitorDetailsHeaderSlot = ({
    source,
    isNew,
    isDraft,
}: {
    source:
        | MonitorV2ControlTestInstanceOverviewResponseDto['source']
        | undefined;
    isNew: boolean;
    isDraft: boolean;
}) => {
    const metadataConfig: {
        condition: boolean;
        config: Pick<MetadataProps, 'colorScheme' | 'label'>;
    }[] = [
        {
            condition: source === 'ACORN',
            config: {
                colorScheme: 'primary',
                label: t`Codebase`,
            },
        },
        {
            condition: isNew,
            config: {
                colorScheme: 'primary',
                label: t`New`,
            },
        },
        {
            condition: isDraft,
            config: {
                colorScheme: 'neutral',
                label: t`Draft`,
            },
        },
    ];

    return metadataConfig.find(({ condition }) => condition);
};

export class MonitoringDetailsPageHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    pageId = 'monitoring-details-overview-page';

    get actionStack(): React.JSX.Element {
        const config =
            sharedMonitoringDetailsActionsController.getActionConfiguration();
        const actions: Action[] = [];

        if (!isEmpty(config.horizontalMenuOptions)) {
            actions.push({
                actionType: 'dropdown',
                id: 'monitoring-details-horizontal-menu',
                typeProps: {
                    label: t`More actions`,
                    isIconOnly: true,
                    level: 'tertiary' as ButtonLevel,
                    startIconName: 'HorizontalMenu',
                    items: config.horizontalMenuOptions,
                },
            });
        }

        // add header actions
        actions.push(...config.headerActions);

        return (
            <ActionStack
                data-id="monitoring-details-header-action-stack"
                gap={dimensionSm}
                data-testid="MonitoringDetailsHeaderActionStack"
                actions={actions}
            />
        );
    }

    get slot(): React.ReactNode {
        const { testDetails, source } = sharedMonitoringTestDetailsController;

        if (!testDetails) {
            return undefined;
        }

        const slotDetails = getMonitorDetailsHeaderSlot({
            source,
            isNew: testDetails.isNew ?? false,
            isDraft: testDetails.draft,
        });

        if (!slotDetails) {
            return undefined;
        }

        return (
            <Metadata
                data-testid="monitoring-header-slot-metadata"
                type="tag"
                colorScheme={slotDetails.config.colorScheme}
                label={slotDetails.config.label}
            />
        );
    }

    get banner(): React.JSX.Element | undefined {
        const { testDetails } = sharedMonitoringTestDetailsController;
        const { currentWorkspaceId } = sharedWorkspacesController;

        if (!testDetails) {
            return undefined;
        }

        const { parentTestId, drafts } = testDetails;
        const hasDrafts = drafts && !isEmpty(drafts);

        if (!parentTestId && !hasDrafts) {
            return undefined;
        }

        let headerTitle: string;
        let buttonTitle: string;
        let paramTestId: number;

        if (parentTestId) {
            headerTitle = t`There is a published version of this test`;
            buttonTitle = t`View published test`;
            paramTestId = parentTestId;
        } else {
            headerTitle = t`There is a draft version of this test`;
            buttonTitle = t`View draft test`;
            paramTestId = head(drafts)?.testId as number;
        }

        return (
            <Banner
                closeButtonAriaLabel="Close"
                data-testid="banner-linked=publish-custom-test"
                displayMode="section"
                severity="primary"
                title={headerTitle}
                body={
                    <Button
                        colorScheme="primary"
                        data-testid="view-draft-test-button"
                        label={buttonTitle}
                        level="secondary"
                        size="sm"
                        width="auto"
                        onClick={() => {
                            sharedProgrammaticNavigationController.navigateTo(
                                `workspaces/${currentWorkspaceId}/compliance/monitoring/production/${paramTestId}/overview`,
                            );
                        }}
                    />
                }
            />
        );
    }

    get title(): string {
        const { testDetails } = sharedMonitoringTestDetailsController;
        const { monitorDetailsData } = activeMonitoringController;
        const name = testDetails?.name || monitorDetailsData?.name;

        return name || t`Loading...`;
    }

    get keyValuePairs(): KeyValuePairProps[] {
        const { testDetails } = sharedMonitoringTestDetailsController;

        if (isNil(testDetails)) {
            return [];
        }

        const {
            checkResultStatus,
            lastCheck,
            source,
            checkTypes,
            checkStatus,
        } = testDetails;

        const testResultMetadata: Pick<KeyValuePairProps, 'value' | 'type'> =
            CheckResultStatus[checkResultStatus] === CheckResultStatus.READY ||
            CheckStatus[checkStatus] === CheckStatus.UNUSED
                ? {
                      value: '—',
                      type: 'TEXT',
                  }
                : {
                      value: {
                          label: capitalize(checkResultStatus),
                          colorScheme: getStatusColorScheme(checkResultStatus),
                          type: 'status' as const,
                      },
                      type: 'BADGE',
                  };

        return [
            {
                id: 'monitoring-header-latest-test-result',
                'data-id': 'monitoring-header-status',
                label: t`Latest test result`,
                ...testResultMetadata,
            },
            {
                id: 'monitoring-header-latest-test-run',
                'data-id': 'monitoring-header-latest-test-run',
                label: t`Latest test run`,
                value:
                    CheckStatus[checkStatus] === CheckStatus.UNUSED
                        ? '—'
                        : formatDate('field_time', lastCheck || undefined),
                type: 'TEXT',
            },
            {
                id: 'monitoring-header-test-lifecycle',
                'data-id': 'monitoring-header-test-lifecycle',
                label: t`Test lifecycle`,
                value: getTestLifecycleLabel(source),
                type: 'TEXT',
            },
            {
                id: 'monitoring-header-category',
                'data-id': 'monitoring-header-category',
                label: t`Category`,
                value: checkTypes[0] ? getCategoryLabel(checkTypes[0]) : '-',
                type: 'TEXT',
            },
        ];
    }
}
