import { isString } from 'lodash-es';
import { z } from 'zod';
import {
    ExternalPolicyFileSelectionField,
    PoliciesDetailsStepRenewalDateFieldComponent,
    ReplacePoliciesButtonField,
} from '@components/policies';
import { sharedPersonnelGroupController } from '@controllers/personnel-group-controller';
import {
    type CreatePolicyFormData,
    sharedPoliciesOwnersController,
} from '@controllers/policies';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { makeAutoObservable } from '@globals/mobx';
import { addMonths, convertToISO8601String } from '@helpers/date-time';
import { AppLink } from '@ui/app-link';
import type { FormSchema } from '@ui/forms';
import type {
    ExternalFile,
    ExternalPolicyProvider,
} from './import-policy-form.types';
import {
    getAllEmployeesLabel,
    getApplicablePersonnelHelpText,
    getApplicablePersonnelLabel,
    getAriaLabelForLearnMoreLink,
    getAtLeastOneGroupError,
    getAtLeastOnePolicyError,
    getAuthorInDrataHelpText,
    getAuthorInDrataLabel,
    getDisclaimerHelpText,
    getDisclaimerLabel,
    getDisclaimerMaxLengthError,
    getLearnMoreLabel,
    getLoadingGroupsLabel,
    getLoadingUsersLabel,
    getNoKeepSeparateLabel,
    getNoPersonnelGroupsFoundLabel,
    getNotApplicableLabel,
    getNotifyNewMembersLabel,
    getNoUsersFoundLabel,
    getOwnerHelpText,
    getOwnerLabel,
    getOwnerPlaceholder,
    getPolicyDescriptionLabel,
    getPolicyDescriptionMaxLengthError,
    getPolicyDescriptionRequiredError,
    getPolicyNameHelpText,
    getPolicyNameLabel,
    getPolicyNameMaxLengthError,
    getPolicyNameRequiredError,
    getPolicySourceLabel,
    getRemoveAllSelectedPersonnelGroupsLabel,
    getRenewalDateLabel,
    getRenewalIntervalLabel,
    getRenewalScheduleOptions,
    getRenewalScheduleTypeMonthValue,
    getSearchGroupsPlaceholder,
    getSelectFileLabel,
    getSelectFileToImportError,
    getSelectOptionError,
    getSelectPersonnelError,
    getSelectPoliciesToReplaceLabel,
    getSelectRenewalDateError,
    getSelectRenewalIntervalError,
    getSelectSourceTypeError,
    getShouldReplacePoliciesHelpText,
    getShouldReplacePoliciesLabel,
    getSpecificGroupsFieldLabel,
    getSpecificGroupsHelpText,
    getSpecificGroupsLabel,
    getUploadExistingPolicyHelpText,
    getUploadExistingPolicyLabel,
    getUploadPolicyFileHelpText,
    getUploadPolicyFileLabel,
    getYesReplaceLabel,
    GROUP_BASED_POLICY_HELP_LINK,
} from './policy-form.constants';
import type { RenewalScheduleType } from './policy-form.types';

/**
 * Model for managing policy form state and providing dynamic form schemas.
 * Provides reactive state management for the wizard form data.
 */
export class PolicyFormModel {
    // ===== OBSERVABLE STATE =====

    /**
     * Whether users data is being loaded.
     */
    isLoadingUsers = false;

    /**
     * Whether replaceable policies data is being loaded.
     */
    isLoadingReplaceablePolicies = false;

    formData: CreatePolicyFormData | undefined;

    /** Additional properties for external import. */
    selectedExternalFile: ExternalFile | null = null;
    connectedProviders: ExternalPolicyProvider[] = [];
    currentProvider: ExternalPolicyProvider | null = null;

    constructor() {
        makeAutoObservable(this, { formData: false });
    }

    setReferenceToModal(formData: CreatePolicyFormData): void {
        this.formData = formData;
    }

    // ===== COMPUTED PROPERTIES =====

    /**
     * User options for the owner combobox with infinite scrolling support.
     */
    get userOptions(): ListBoxItemData[] {
        return sharedPoliciesOwnersController.ownersComboboxOptions;
    }

    /**
     * Handler for fetching user options with search support.
     */
    handleFetchUserOptions = ({
        search,
    }: {
        search?: string;
        increasePage?: boolean;
    }): void => {
        sharedPoliciesOwnersController.loadNextPage({ search });
    };

    /**
     * Handler for fetching personnel group options with search support.
     */
    handleFetchPersonnelGroupOptions = ({
        search,
        increasePage,
    }: {
        search?: string;
        increasePage?: boolean;
    }): void => {
        if (increasePage) {
            sharedPersonnelGroupController.loadNextPage({ search });

            return;
        }

        sharedPersonnelGroupController.loadPersonnelGroups({
            q: search?.trim(),
        });
    };

    /**
     * Group options for the personnel groups combobox.
     */
    get groupOptions(): ListBoxItemData[] {
        return sharedPersonnelGroupController.personnelGroups.map((group) => ({
            id: group.id.toString(),
            label: group.name,
            value: group.id.toString(),
            description: `${group.membersCount} members`,
        }));
    }

    /**
     * Form schema for the Policy Source step with dynamic options.
     */
    get policySourceSchema(): FormSchema {
        return {
            sourceType: {
                type: 'choiceCardGroup',
                label: getPolicySourceLabel(),
                choiceCardInputType: 'radio',
                cosmosUseWithCaution_forceOptionOrientation: 'vertical',
                isOptional: false,
                initialValue: this.formData?.source.sourceType,
                options: [
                    {
                        label: getUploadExistingPolicyLabel(),
                        value: 'UPLOADED',
                        helpText: getUploadExistingPolicyHelpText(),
                    },
                    {
                        label: getAuthorInDrataLabel(),
                        value: 'BUILDER',
                        helpText: getAuthorInDrataHelpText(),
                    },
                ],
                validator: z
                    .string({
                        required_error: getSelectSourceTypeError(),
                        invalid_type_error: getSelectSourceTypeError(),
                    })
                    .refine(
                        (value) => ['BUILDER', 'UPLOADED'].includes(value),
                        {
                            message: getSelectSourceTypeError(),
                        },
                    ),
            },
            uploadedFile: {
                type: 'file',
                label: getUploadPolicyFileLabel(),
                helpText: getUploadPolicyFileHelpText(),
                acceptedFormats: ['pdf', 'doc', 'docx', 'txt'],
                maxFileSizeInBytes: 10485760, // 10MB
                isMulti: false,
                shownIf: {
                    fieldName: 'sourceType',
                    operator: 'equals',
                    value: 'UPLOADED',
                },
            },
        };
    }

    /**
     * Form schema for the Details step with dynamic options.
     */
    get detailsSchema(): FormSchema {
        return {
            name: {
                type: 'text',
                label: getPolicyNameLabel(),
                helpText: getPolicyNameHelpText(),
                initialValue: this.formData?.details.name || '',
                validator: z
                    .string()
                    .min(1, getPolicyNameRequiredError())
                    .max(192, getPolicyNameMaxLengthError()),
            },
            description: {
                type: 'textarea',
                label: getPolicyDescriptionLabel(),
                initialValue: this.formData?.details.description || '',
                validator: z
                    .string()
                    .min(1, getPolicyDescriptionRequiredError())
                    .max(30000, getPolicyDescriptionMaxLengthError()),
            },
            renewalDate: {
                type: 'custom',
                label: getRenewalDateLabel(),
                render: PoliciesDetailsStepRenewalDateFieldComponent,
                customType: 'object',
                fields: {
                    renewalFrequency: {
                        type: 'select',
                        label: getRenewalIntervalLabel(),
                        shouldHideLabel: true,
                        // helpText: getRenewalIntervalHelpText(),
                        options: getRenewalScheduleOptions(),
                        initialValue:
                            this.formData?.details.renewalDate
                                .renewalFrequency ??
                            getRenewalScheduleOptions()[0], // Default to 1 Month
                        validator: z.object(
                            {
                                id: z.string(),
                                label: z.string(),
                                value: z.string(),
                            },
                            {
                                required_error: getSelectRenewalIntervalError(),
                                invalid_type_error:
                                    getSelectRenewalIntervalError(),
                            },
                        ),
                    },
                    renewalDate: {
                        type: 'date',
                        label: getRenewalDateLabel(),
                        shouldHideLabel: true,
                        isMulti: false,
                        initialValue: (this.formData?.details.renewalDate
                            .renewalDate ??
                            this.calculateRenewalDate(
                                'ONE_MONTH',
                            )) as `${number}${number}${number}${number}-${number}${number}-${number}${number}`,
                        getIsDateUnavailable: (date) => {
                            return (
                                new Date(date).toISOString().split('T')[0] <
                                new Date().toISOString().split('T')[0]
                            );
                        },
                        validator: z
                            .string({
                                errorMap: () => ({
                                    message: getSelectRenewalDateError(),
                                }),
                            })
                            .min(1, getSelectRenewalDateError()),
                    },
                },
            },
            owner: {
                type: 'combobox',
                label: getOwnerLabel(),
                helpText: getOwnerHelpText(),
                isMultiSelect: false,
                initialValue: this.formData?.details.owner
                    ? {
                          id: this.formData.details.owner.id,
                          label:
                              this.formData.details.owner.label ||
                              this.formData.details.owner.id,
                          value:
                              this.formData.details.owner.value ||
                              this.formData.details.owner.id,
                      }
                    : undefined,
                options: this.userOptions,
                isLoading: sharedPoliciesOwnersController.isLoading,
                loaderLabel: getLoadingUsersLabel(),
                onFetchOptions: this.handleFetchUserOptions,
                disableFetchOnMount: true,
                hasMore: sharedPoliciesOwnersController.hasNextPage,
                placeholder: getOwnerPlaceholder(),
                getSearchEmptyState: getNoUsersFoundLabel,
            },
            disclaimer: {
                type: 'textarea',
                label: getDisclaimerLabel(),
                helpText: getDisclaimerHelpText(),
                maxCharacters: 1000,
                initialValue: this.formData?.details.disclaimer || '',
                isOptional: true,
                validator: z.preprocess(
                    (val: unknown) => (isString(val) ? val.trim() : val),
                    z.string().max(1000, getDisclaimerMaxLengthError()),
                ),
            },
        };
    }

    /**
     * Form schema for the Personnel Groups step with dynamic options.
     */
    get personnelGroupsSchema(): FormSchema {
        const { isPersonnelGroupsEnabled, isInfiniteLoading, hasNextPage } =
            sharedPersonnelGroupController;

        return {
            assignedTo: {
                type: 'radioGroup',
                label: getApplicablePersonnelLabel(),
                helpText: getApplicablePersonnelHelpText(),
                cosmosUseWithCaution_forceOptionOrientation: 'vertical',
                initialValue: this.formData?.personnelGroups.assignedTo,
                // TODO: logic around if no idp groups
                options: [
                    {
                        label: getAllEmployeesLabel(),
                        value: 'ALL',
                    },
                    ...(isPersonnelGroupsEnabled
                        ? [
                              {
                                  label: getSpecificGroupsLabel(),
                                  value: 'GROUP',
                              },
                          ]
                        : []),
                    {
                        label: getNotApplicableLabel(),
                        value: 'NONE',
                    },
                ],
                validator: z.enum(['ALL', 'GROUP', 'NONE'], {
                    errorMap: () => ({
                        message: getSelectPersonnelError(),
                    }),
                }),
            },
            selectedGroups: {
                type: 'combobox',
                label: getSpecificGroupsFieldLabel(),
                helpText: getSpecificGroupsHelpText(),
                isMultiSelect: true,
                initialValue:
                    this.formData?.personnelGroups.selectedGroups ?? [],
                placeholder: getSearchGroupsPlaceholder(),
                isOptional: false,
                options: this.groupOptions,
                isLoading: isInfiniteLoading,
                loaderLabel: getLoadingGroupsLabel(),
                onFetchOptions: this.handleFetchPersonnelGroupOptions,
                hasMore: hasNextPage,
                getSearchEmptyState: getNoPersonnelGroupsFoundLabel,
                removeAllSelectedItemsLabel:
                    getRemoveAllSelectedPersonnelGroupsLabel(),
                shownIf: {
                    fieldName: 'assignedTo',
                    operator: 'equals',
                    value: 'GROUP',
                },
                validator: z
                    .array(
                        z.object({
                            id: z.string(),
                            label: z.string(),
                            value: z.string(),
                        }),
                    )
                    .min(1, getAtLeastOneGroupError())
                    .optional(),
            },
            notifyNewMembers: {
                type: 'checkbox',
                label: getNotifyNewMembersLabel(),
                value: 'notify',
                initialValue:
                    this.formData?.personnelGroups.notifyNewMembers ?? false,
                shownIf: {
                    fieldName: 'assignedTo',
                    operator: 'equals',
                    value: 'GROUP',
                },
            },
            appLink: {
                type: 'custom',
                label: 'App Link',
                render: () => (
                    <AppLink
                        isExternal
                        aria-label={getAriaLabelForLearnMoreLink()}
                        href={GROUP_BASED_POLICY_HELP_LINK}
                        data-id="DIee8eB6"
                    >
                        {getLearnMoreLabel()}
                    </AppLink>
                ),
                shownIf: {
                    fieldName: 'assignedTo',
                    operator: 'equals',
                    value: 'GROUP',
                },
            },
        };
    }
    /** External source step schema. */
    get externalSourceSchema(): FormSchema {
        return {
            externalFileId: {
                type: 'custom',
                label: getSelectFileLabel(),
                render: (props) => (
                    <ExternalPolicyFileSelectionField
                        {...props}
                        data-id="IwBn8Ex8"
                    />
                ),
                initialValue: this.formData?.externalSource.externalFileId,
                validator: z.string({
                    required_error: getSelectFileToImportError(),
                    invalid_type_error: getSelectFileToImportError(),
                }),
            },
        };
    }

    /**
     * Form schema for the Replace Policies step with dynamic options.
     */
    get replacePoliciesSchema(): FormSchema {
        return {
            shouldReplacePolicies: {
                type: 'radioGroup',
                label: getShouldReplacePoliciesLabel(),
                helpText: getShouldReplacePoliciesHelpText(),
                cosmosUseWithCaution_forceOptionOrientation: 'vertical',
                initialValue:
                    this.formData?.replacePolicies.shouldReplacePolicies ??
                    'no',
                options: [
                    {
                        label: getYesReplaceLabel(),
                        value: 'yes',
                    },
                    {
                        label: getNoKeepSeparateLabel(),
                        value: 'no',
                    },
                ],
                validator: z.enum(['yes', 'no'], {
                    errorMap: () => ({ message: getSelectOptionError() }),
                }),
            },
            policiesToReplace: {
                type: 'custom',
                label: getSelectPoliciesToReplaceLabel(),
                render: ReplacePoliciesButtonField,
                initialValue:
                    this.formData?.replacePolicies.policiesToReplace ?? [],
                shownIf: {
                    fieldName: 'shouldReplacePolicies',
                    operator: 'equals',
                    value: 'yes',
                },
                validator: z
                    .array(
                        z.object({
                            id: z.number(),
                            name: z.string(),
                        }),
                    )
                    .min(1, getAtLeastOnePolicyError())
                    .optional(),
            },
        };
    }

    get isBambooHRProvider(): boolean {
        return this.currentProvider?.type === 'BAMBOO_HR';
    }

    get isConfluenceProvider(): boolean {
        return this.currentProvider?.type === 'CONFLUENCE';
    }
    get isNotionProvider(): boolean {
        return this.currentProvider?.type === 'NOTION';
    }

    /** Provider-specific banners. */
    get shouldShowProviderBanner(): boolean {
        return this.isNotionProvider || this.isBambooHRProvider;
    }

    get providerBannerType(): 'NOTION' | 'BAMBOO_HR' {
        return this.isNotionProvider ? 'NOTION' : 'BAMBOO_HR';
    }

    // ===== ACTIONS =====

    /**
     * Load users for the owner selection.
     */
    loadUsers = (): void => {
        // Initialize the policies owners controller which handles infinite scrolling
        sharedPoliciesOwnersController.initializePoliciesOwners();
    };

    /**
     * Calculate renewal date based on the selected interval.
     *
     * @param intervalValue - The selected renewal interval value.
     * @returns The calculated renewal date as ISO string.
     */
    calculateRenewalDate = (intervalValue: string): string => {
        const startDate = new Date();

        // If custom is selected, return today's date for manual selection
        if (intervalValue === 'CUSTOM') {
            return startDate.toISOString().split('T')[0];
        }

        // Get the number of months to add
        const monthsToAdd = getRenewalScheduleTypeMonthValue(
            intervalValue as RenewalScheduleType,
        );

        // Calculate the new date
        const renewalDate = new Date(startDate);

        const newRenewalDate = addMonths(renewalDate, monthsToAdd);

        return convertToISO8601String(newRenewalDate);
    };

    /**
     * Reset the model to initial state.
     */
    reset = (): void => {
        this.isLoadingUsers = false;
        this.formData = undefined;
    };
}
