import { routeController } from '@controllers/route';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { AppLink } from '@ui/app-link';

export class FrameworkCreatePageHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    pageId = 'create-framework';

    title = t`Create framework`;

    isCentered = true;

    get backLink(): React.JSX.Element {
        const { userPartOfUrl } = routeController;

        return (
            <AppLink
                href={`${userPartOfUrl}/compliance/frameworks`}
                data-testid="BackLink"
                size="sm"
            >
                {t`Back to frameworks`}
            </AppLink>
        );
    }
}
