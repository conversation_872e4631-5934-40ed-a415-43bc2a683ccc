import { sharedFrameworkDetailsController } from '@controllers/frameworks';
import { routeController } from '@controllers/route';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { AppLink } from '@ui/app-link';

export class FrameworkCreateRequirementPageHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    pageId = 'create-requirement';

    title = t`Add requirement`;
    isCentered = true;

    get backLink(): React.JSX.Element {
        const { userPartOfUrl } = routeController;
        const { frameworkDetails } = sharedFrameworkDetailsController;

        return (
            <AppLink
                href={`${userPartOfUrl}/compliance/frameworks/all/current/${frameworkDetails?.id}/requirements`}
                data-testid="BackLink"
                size="sm"
            >
                {t`Back to requirements`}
            </AppLink>
        );
    }
}
