import { openFilePreviewModal } from '@components/evidence-library';
import { sharedAuditHubAuditController } from '@controllers/audit-hub';
import {
    sharedAuditEvidenceQueryController,
    sharedAuditHubAuditorClientEvidenceStatusController,
    sharedCompanyArchiveStatusQueryController,
} from '@controllers/audit-hub-auditor-client-actions';
import { sharedCustomerRequestsController } from '@controllers/customer-requests';
import { snackbarController } from '@controllers/snackbar';
// eslint-disable-next-line no-restricted-imports -- It will not work if I use AppLink
import { Link } from '@cosmos/components/link';
import { AsyncActionType, AsyncEventType, SocketEvent } from '@drata/enums';
import { makeAutoObservable, runInAction, when } from '@globals/mobx';
import { sharedSocketController } from '@globals/socket';
import { downloadFileFromSignedUrl } from '@helpers/download-file';
import {
    downloadActionsType,
    getDownloadTypeDescriptor,
} from './constants/async-events.constants';
import type {
    AsyncEventData,
    DownloadEventData,
    PreviewEventData,
} from './types/async-events.types';

class AsyncEventsController {
    isInitialized = false;

    handlers: Partial<
        Record<AsyncActionType, (event: AsyncEventData) => void>
    > = {};

    socketCallback: (data: AsyncEventData) => void;

    constructor() {
        makeAutoObservable(this);

        this.socketCallback = this.handleSocketEvent.bind(this);

        this.registerHandler(
            AsyncActionType.EVENT_DETAILS_FILE_PREVIEW,
            this.handlePreview.bind(this),
        );

        this.registerHandler(
            AsyncActionType.COMPANY_ARCHIVES_DOWNLOAD_EVIDENCE,
            this.handleCompanyArchivesDownload.bind(this),
        );

        downloadActionsType.forEach((downloadAction) => {
            this.registerHandler(
                downloadAction,
                this.handleDownload.bind(this),
            );
        });
    }

    init(): void {
        if (this.isInitialized) {
            return;
        }

        when(
            () => sharedSocketController.isInitialized,
            () => {
                runInAction(() => {
                    this.subscribeToSocket();
                    this.isInitialized = true;
                });
            },
        );
    }

    private subscribeToSocket(): void {
        sharedSocketController.subscribe({
            channelType: 'user',
            eventName: SocketEvent.SNACK_NOTIFICATION,
            callback: this.socketCallback,
        });
    }

    registerHandler(
        actionType: AsyncActionType,
        fn: (event: AsyncEventData) => void,
    ) {
        this.handlers[actionType] = fn;
    }

    private handleSocketEvent(data: AsyncEventData) {
        const handler = this.handlers[data.actionType];

        if (handler) {
            handler(data);
        }
    }

    private handleDownload(event: AsyncEventData): void {
        const downloadEvent = event as DownloadEventData;
        const { actionType, type, metadata, additionalData } = downloadEvent;

        const descriptor = getDownloadTypeDescriptor(actionType, type);

        if (!descriptor) {
            return;
        }
        const { title, message: description, severity } = descriptor;
        const timestamp = new Date().toISOString();

        let linkProp: { link: ReturnType<typeof Link> } | null = null;

        if (type === AsyncEventType.COMPLETE && metadata.url) {
            linkProp = {
                link: Link({
                    href: '#',
                    /**
                     * TODO: Buttons that look like links https://drata.atlassian.net/browse/ENG-25594.
                     */
                    onClick: () => {
                        downloadFileFromSignedUrl(additionalData.signedUrl);
                    },
                    children: 'Download',
                }),
            };
        }

        snackbarController.addSnackbar({
            id: `${timestamp}-download-${type.toLowerCase()}`,
            hasTimeout: type === AsyncEventType.COMPLETE ? false : undefined,
            props: {
                title,
                description,
                severity,
                closeButtonAriaLabel: 'Close',
                ...linkProp,
            },
        });
    }

    private handlePreview(event: AsyncEventData): void {
        const previewEvent = event as PreviewEventData;

        const { actionType, type, additionalData } = previewEvent;

        const descriptor = getDownloadTypeDescriptor(actionType, type);

        if (!descriptor) {
            return;
        }

        const { title, message: description, severity } = descriptor;
        const timestamp = new Date().toISOString();

        let linkProp: { link: ReturnType<typeof Link> } | null = null;

        if (type === AsyncEventType.COMPLETE && additionalData.signedUrl) {
            linkProp = {
                link: Link({
                    label: 'Preview',
                    href: '#',
                    onClick: () => {
                        openFilePreviewModal({
                            eventFileName: additionalData.fileName,
                            eventFileUrl: additionalData.signedUrl,
                        });
                    },
                }),
            };
        }

        snackbarController.addSnackbar({
            id: `${timestamp}-preview-${type.toLowerCase()}`,
            props: {
                title,
                description,
                severity,
                closeButtonAriaLabel: 'Close',
                ...linkProp,
            },
        });
    }

    private handleCompanyArchivesDownload(): void {
        sharedAuditEvidenceQueryController.loadLatestAuditorFrameworkEvidencePingQuery(
            sharedCustomerRequestsController.auditId,
        );
        sharedAuditHubAuditorClientEvidenceStatusController.updateEvidenceExpiredStatus();
        sharedAuditHubAuditController.isPreAuditPackageReady = true;
        sharedCompanyArchiveStatusQueryController.loadLatestCompanyArchiveStatus(
            'PRE_AUDIT',
        );
    }
}

export const sharedAsyncEventsController = new AsyncEventsController();
